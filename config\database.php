<?php
/**
 * Database Configuration for SC_IMS
 * Sports Competition and Event Management System
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'SC_IMS_db';
    private $username = 'root';
    private $password = '';
    private $conn;

    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }

    /**
     * Create database if it doesn't exist
     */
    public function createDatabase() {
        try {
            $conn = new PDO(
                "mysql:host=" . $this->host,
                $this->username,
                $this->password
            );
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8 COLLATE utf8_general_ci";
            $conn->exec($sql);
            
            return true;
        } catch(PDOException $e) {
            echo "Database creation error: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Initialize database tables
     */
    public function initializeTables() {
        $conn = $this->getConnection();
        
        if (!$conn) {
            return false;
        }

        try {
            // Events table
            $sql = "CREATE TABLE IF NOT EXISTS events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                status ENUM('upcoming', 'ongoing', 'completed', 'cancelled') DEFAULT 'upcoming',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Sports table
            $sql = "CREATE TABLE IF NOT EXISTS sports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                type ENUM('traditional', 'judged', 'academic', 'custom') NOT NULL,
                scoring_method ENUM('point_based', 'set_based', 'criteria_based', 'time_based') NOT NULL,
                bracket_format ENUM('single_elimination', 'double_elimination', 'round_robin', 'multi_stage') NOT NULL,
                rules TEXT,
                max_participants INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Departments table
            $sql = "CREATE TABLE IF NOT EXISTS departments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                abbreviation VARCHAR(10) NOT NULL UNIQUE,
                color_code VARCHAR(7) DEFAULT '#000000',
                contact_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Event Sports table (many-to-many relationship)
            $sql = "CREATE TABLE IF NOT EXISTS event_sports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_id INT NOT NULL,
                sport_id INT NOT NULL,
                max_teams INT DEFAULT 8,
                registration_deadline DATETIME,
                venue VARCHAR(255),
                status ENUM('registration', 'ongoing', 'completed') DEFAULT 'registration',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE,
                UNIQUE KEY unique_event_sport (event_id, sport_id)
            )";
            $conn->exec($sql);

            // Registrations table
            $sql = "CREATE TABLE IF NOT EXISTS registrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                department_id INT NOT NULL,
                team_name VARCHAR(255),
                participants JSON,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('registered', 'confirmed', 'cancelled') DEFAULT 'registered',
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
                UNIQUE KEY unique_registration (event_sport_id, department_id)
            )";
            $conn->exec($sql);

            // Brackets table
            $sql = "CREATE TABLE IF NOT EXISTS brackets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                bracket_data JSON NOT NULL,
                bracket_type ENUM('single_elimination', 'double_elimination', 'round_robin', 'multi_stage') NOT NULL,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Matches table
            $sql = "CREATE TABLE IF NOT EXISTS matches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                team1_id INT NOT NULL,
                team2_id INT,
                round_number INT DEFAULT 1,
                match_number INT DEFAULT 1,
                scheduled_time DATETIME,
                actual_start_time DATETIME,
                actual_end_time DATETIME,
                status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
                winner_id INT,
                venue VARCHAR(255),
                referee_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                FOREIGN KEY (team1_id) REFERENCES registrations(id) ON DELETE CASCADE,
                FOREIGN KEY (team2_id) REFERENCES registrations(id) ON DELETE CASCADE,
                FOREIGN KEY (winner_id) REFERENCES registrations(id) ON DELETE SET NULL
            )";
            $conn->exec($sql);

            // Scores table
            $sql = "CREATE TABLE IF NOT EXISTS scores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                match_id INT NOT NULL,
                team1_score INT DEFAULT 0,
                team2_score INT DEFAULT 0,
                period INT DEFAULT 1,
                notes TEXT,
                is_final BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Rankings table
            $sql = "CREATE TABLE IF NOT EXISTS rankings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_id INT NOT NULL,
                department_id INT NOT NULL,
                total_points DECIMAL(10,2) DEFAULT 0,
                wins INT DEFAULT 0,
                losses INT DEFAULT 0,
                draws INT DEFAULT 0,
                rank_position INT DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
                UNIQUE KEY unique_event_department (event_id, department_id)
            )";
            $conn->exec($sql);

            // Admin users table (for admin authentication only)
            $sql = "CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Admin sessions table
            $sql = "CREATE TABLE IF NOT EXISTS admin_sessions (
                id VARCHAR(128) PRIMARY KEY,
                admin_user_id INT NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Audit logs table
            $sql = "CREATE TABLE IF NOT EXISTS audit_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_user_id INT,
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50) NOT NULL,
                record_id INT,
                old_values JSON,
                new_values JSON,
                ip_address VARCHAR(45),
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE SET NULL
            )";
            $conn->exec($sql);

            // Create default admin user after tables are created
            $this->createDefaultAdmin();

            return true;
        } catch(PDOException $e) {
            echo "Table creation error: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Create default admin user
     */
    public function createDefaultAdmin() {
        $conn = $this->getConnection();

        if (!$conn) {
            return false;
        }

        try {
            // Check if admin already exists
            $sql = "SELECT id FROM admin_users WHERE username = 'admin' LIMIT 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute();

            if ($stmt->fetch()) {
                return true; // Admin already exists
            }

            // Create default admin
            $sql = "INSERT INTO admin_users (username, email, password_hash, full_name, is_active)
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                'admin',
                '<EMAIL>',
                password_hash('admin123', PASSWORD_DEFAULT),
                'System Administrator',
                1
            ]);

            return true;
        } catch (Exception $e) {
            error_log("Error creating default admin: " . $e->getMessage());
            return false;
        }
    }
}
?>
