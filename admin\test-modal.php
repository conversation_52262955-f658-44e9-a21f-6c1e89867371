<?php
require_once 'auth.php';
requireAdmin();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test - SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <div class="admin-container">
        <div class="main-content">
            <div class="content-header">
                <h1>Modal System Test</h1>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3>Test Modal Functionality</h3>
                </div>
                <div class="card-body">
                    <button class="btn-modal-trigger" onclick="console.log('Test button clicked'); openModal('testModal');">
                        <i class="fas fa-plus"></i>
                        Test Modal
                    </button>
                    
                    <div style="margin-top: 20px;">
                        <h4>Debug Information:</h4>
                        <div id="debug-info">
                            <p>jQuery loaded: <span id="jquery-status">Checking...</span></p>
                            <p>ModalManager available: <span id="modal-manager-status">Checking...</span></p>
                            <p>openModal function available: <span id="open-modal-status">Checking...</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div id="testModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">Test Modal</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>This is a test modal. If you can see this, the modal system is working!</p>
                <form>
                    <div class="form-group">
                        <label for="testInput">Test Input:</label>
                        <input type="text" id="testInput" name="testInput" class="form-control" placeholder="Enter test text">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary">Test Button</button>
            </div>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Check if everything is loaded properly
        $(document).ready(function() {
            setTimeout(function() {
                $('#jquery-status').text(typeof $ !== 'undefined' ? 'Yes' : 'No');
                $('#modal-manager-status').text(typeof window.modalManager !== 'undefined' ? 'Yes' : 'No');
                $('#open-modal-status').text(typeof window.openModal !== 'undefined' ? 'Yes' : 'No');
            }, 1000);
        });
    </script>
</body>
</html>
