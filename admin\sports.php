<?php
/**
 * Sports Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'create':
                $result = createSport($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update':
                $result = updateSport($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'delete':
                $result = deleteSport($conn, $_POST['sport_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get all sports
$sports = getAllSports($conn);

// Get sport for editing
$edit_sport = null;
if (isset($_GET['edit'])) {
    $edit_sport = getSportById($conn, $_GET['edit']);
}

function createSport($conn, $data) {
    try {
        $sql = "INSERT INTO sports (name, description, type, scoring_system, rules, max_participants_per_team, min_participants_per_team) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['description']),
            $data['type'],
            $data['scoring_system'],
            sanitizeInput($data['rules']),
            $data['max_participants_per_team'] ?: null,
            $data['min_participants_per_team'] ?: null
        ]);
        
        logActivity('CREATE_SPORT', 'sports', $conn->lastInsertId());
        return ['success' => true, 'message' => 'Sport created successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to create sport: ' . $e->getMessage()];
    }
}

function updateSport($conn, $data) {
    try {
        $sql = "UPDATE sports SET name = ?, description = ?, type = ?, scoring_system = ?, rules = ?, max_participants_per_team = ?, min_participants_per_team = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['description']),
            $data['type'],
            $data['scoring_system'],
            sanitizeInput($data['rules']),
            $data['max_participants_per_team'] ?: null,
            $data['min_participants_per_team'] ?: null,
            $data['sport_id']
        ]);
        
        logActivity('UPDATE_SPORT', 'sports', $data['sport_id']);
        return ['success' => true, 'message' => 'Sport updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update sport: ' . $e->getMessage()];
    }
}

function deleteSport($conn, $sport_id) {
    try {
        $sql = "DELETE FROM sports WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$sport_id]);
        
        logActivity('DELETE_SPORT', 'sports', $sport_id);
        return ['success' => true, 'message' => 'Sport deleted successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to delete sport: ' . $e->getMessage()];
    }
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .sports-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .sport-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .sports-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .type-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .type-traditional { background: #d4edda; color: #155724; }
        .type-academic { background: #d1ecf1; color: #0c5460; }
        .type-cultural { background: #fff3cd; color: #856404; }
        .type-esports { background: #e2e3e5; color: #383d41; }
        
        .scoring-badge {
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 0.75rem;
            background: #f8f9fa;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>Sports Management</h1>
                </div>
                <div class="admin-nav">
                    <a href="index.php">Dashboard</a>
                    <a href="../index.php">Public View</a>
                    </div>
            </header>

            <div class="admin-content">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <div class="sports-header">
                    <h2><?php echo $edit_sport ? 'Edit Sport' : 'Add New Sport'; ?></h2>
                    <?php if ($edit_sport): ?>
                        <a href="sports.php" class="btn btn-secondary">Cancel Edit</a>
                    <?php endif; ?>
                </div>

                <!-- Sport Form -->
                <div class="sport-form">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $edit_sport ? 'update' : 'create'; ?>">
                        <?php if ($edit_sport): ?>
                            <input type="hidden" name="sport_id" value="<?php echo $edit_sport['id']; ?>">
                        <?php endif; ?>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Sport Name *</label>
                                <input type="text" id="name" name="name" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_sport['name'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="type">Type</label>
                                <select id="type" name="type" class="form-control">
                                    <option value="traditional" <?php echo ($edit_sport['type'] ?? '') === 'traditional' ? 'selected' : ''; ?>>Traditional Sports</option>
                                    <option value="academic" <?php echo ($edit_sport['type'] ?? '') === 'academic' ? 'selected' : ''; ?>>Academic Competition</option>
                                    <option value="cultural" <?php echo ($edit_sport['type'] ?? '') === 'cultural' ? 'selected' : ''; ?>>Cultural Event</option>
                                    <option value="esports" <?php echo ($edit_sport['type'] ?? '') === 'esports' ? 'selected' : ''; ?>>E-Sports</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-control" rows="3"><?php echo htmlspecialchars($edit_sport['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="scoring_system">Scoring System</label>
                                <select id="scoring_system" name="scoring_system" class="form-control">
                                    <option value="points" <?php echo ($edit_sport['scoring_system'] ?? '') === 'points' ? 'selected' : ''; ?>>Points Based</option>
                                    <option value="time" <?php echo ($edit_sport['scoring_system'] ?? '') === 'time' ? 'selected' : ''; ?>>Time Based</option>
                                    <option value="sets" <?php echo ($edit_sport['scoring_system'] ?? '') === 'sets' ? 'selected' : ''; ?>>Sets/Games</option>
                                    <option value="criteria" <?php echo ($edit_sport['scoring_system'] ?? '') === 'criteria' ? 'selected' : ''; ?>>Criteria Based</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="min_participants_per_team">Min Participants</label>
                                <input type="number" id="min_participants_per_team" name="min_participants_per_team" class="form-control" 
                                       value="<?php echo $edit_sport['min_participants_per_team'] ?? ''; ?>" min="1">
                            </div>
                            <div class="form-group">
                                <label for="max_participants_per_team">Max Participants</label>
                                <input type="number" id="max_participants_per_team" name="max_participants_per_team" class="form-control" 
                                       value="<?php echo $edit_sport['max_participants_per_team'] ?? ''; ?>" min="1">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="rules">Rules & Regulations</label>
                            <textarea id="rules" name="rules" class="form-control" rows="5"><?php echo htmlspecialchars($edit_sport['rules'] ?? ''); ?></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_sport ? 'Update Sport' : 'Add Sport'; ?>
                        </button>
                    </form>
                </div>

                <!-- Sports Table -->
                <div class="sports-table">
                    <div class="card-header">
                        <h3>All Sports</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($sports)): ?>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Scoring System</th>
                                        <th>Participants</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sports as $sport): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($sport['name']); ?></strong></td>
                                        <td>
                                            <span class="type-badge type-<?php echo $sport['type']; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $sport['type'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="scoring-badge">
                                                <?php echo ucfirst($sport['scoring_system']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            $min = $sport['min_participants_per_team'] ?? 'N/A';
                                            $max = $sport['max_participants_per_team'] ?? 'N/A';
                                            echo $min . ' - ' . $max;
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars(substr($sport['description'], 0, 80)) . (strlen($sport['description']) > 80 ? '...' : ''); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="sports.php?edit=<?php echo $sport['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this sport?');">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="sport_id" value="<?php echo $sport['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No sports found. Add your first sport above.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
</body>
</html>
