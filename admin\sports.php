<?php
/**
 * Sports Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get all sports with enhanced type information
$stmt = $conn->prepare("
    SELECT
        s.*,
        st.name as sport_type_name,
        st.description as sport_type_description,
        st.category as sport_type_category,
        st.color_code,
        st.icon_class
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    ORDER BY s.display_order ASC, s.name ASC
");
$stmt->execute();
$sports = $stmt->fetchAll();

// Get all sport types for the form dropdown
$stmt = $conn->prepare("SELECT * FROM sport_types ORDER BY name ASC");
$stmt->execute();
$sportTypes = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Management - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-trophy"></i>
                        <span>Sports Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Sports Management</h1>
                <p class="page-description">Create, edit, and manage sports and competition types</p>
            </div>

            <!-- Sports Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Sports</h3>
                    <button class="btn-modal-trigger" onclick="openModal('sportModal')">
                        <i class="fas fa-plus"></i>
                        Add New Sport
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($sports)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Type</th>
                                    <th>Scoring System</th>
                                    <th>Participants</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sports as $sport): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($sport['name'] ?? ''); ?></strong></td>
                                    <td><?php echo htmlspecialchars(substr($sport['description'] ?? '', 0, 80)) . (strlen($sport['description'] ?? '') > 80 ? '...' : ''); ?></td>
                                    <td>
                                        <?php if ($sport['sport_type_name']): ?>
                                            <div class="sport-type-badge" style="background-color: <?php echo $sport['color_code'] ?? '#007bff'; ?>;">
                                                <i class="<?php echo $sport['icon_class'] ?? 'fas fa-trophy'; ?>"></i>
                                                <span><?php echo htmlspecialchars($sport['sport_type_name']); ?></span>
                                            </div>
                                            <small class="sport-type-description">
                                                <?php echo htmlspecialchars(substr($sport['sport_type_description'] ?? '', 0, 60)) . (strlen($sport['sport_type_description'] ?? '') > 60 ? '...' : ''); ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="status-badge status-<?php echo $sport['type'] ?? 'individual'; ?>">
                                                <?php echo ucfirst($sport['type'] ?? 'individual'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo ucfirst($sport['scoring_method'] ?? 'points'); ?></td>
                                    <td>
                                        <?php if ($sport['max_participants'] > 0): ?>
                                            Max: <?php echo $sport['max_participants']; ?>
                                        <?php else: ?>
                                            Unlimited
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editSport(<?php echo $sport['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn-delete" onclick="deleteSport(<?php echo $sport['id']; ?>, '<?php echo htmlspecialchars($sport['name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-trophy" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="font-size: 18px; margin-bottom: 8px;">No sports found</p>
                            <p style="margin-bottom: 20px;">Create your first sport to get started</p>
                            <button class="btn-modal-trigger" onclick="openModal('sportModal')">
                                <i class="fas fa-plus"></i>
                                Create First Sport
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sport Modal -->
    <div id="sportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="sportModalTitle">Add New Sport</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="sport">
                <input type="hidden" name="action" value="create" id="sportAction">
                <input type="hidden" name="id" id="sportId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sportName">Sport Name *</label>
                            <input type="text" id="sportName" name="name" class="form-control" required>
                            <div class="error-message">Sport name is required</div>
                        </div>
                        <div class="form-group">
                            <label for="sportTypeId">Sport Type *</label>
                            <select id="sportTypeId" name="sport_type_id" class="form-control" required onchange="updateSportTypeInfo()">
                                <option value="">Select Sport Type</option>
                                <?php foreach ($sportTypes as $sportType): ?>
                                    <option value="<?php echo $sportType['id']; ?>"
                                            data-category="<?php echo $sportType['category']; ?>"
                                            data-description="<?php echo htmlspecialchars($sportType['description']); ?>"
                                            data-color="<?php echo $sportType['color_code']; ?>">
                                        <?php echo htmlspecialchars($sportType['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="sport-type-info" id="sportTypeInfo" style="display: none;">
                                <small class="text-muted" id="sportTypeDescription"></small>
                            </div>
                            <div class="error-message">Please select a sport type</div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sportDescription">Description</label>
                        <textarea id="sportDescription" name="description" class="form-control" rows="3" placeholder="Enter sport description..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sportScoringMethod">Scoring Method</label>
                            <select id="sportScoringMethod" name="scoring_method" class="form-control">
                                <option value="point_based">Point Based</option>
                                <option value="time_based">Time Based</option>
                                <option value="criteria_based">Criteria Based (Judged)</option>
                                <option value="set_based">Set Based</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="sportMaxParticipants">Max Participants</label>
                            <input type="number" id="sportMaxParticipants" name="max_participants" class="form-control" placeholder="0 for unlimited" min="0">
                            <small class="text-muted">Set to 0 for unlimited participants</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sportRules">Rules & Regulations</label>
                        <textarea id="sportRules" name="rules" class="form-control" rows="4" placeholder="Enter sport rules and regulations..."></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="sportSubmitBtn">Create Sport</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Sport-specific JavaScript functions
        function toggleParticipantFields() {
            const sportType = document.getElementById('sportType').value;
            const participantFields = document.getElementById('participantFields');
            
            if (sportType === 'team') {
                participantFields.style.display = 'block';
            } else {
                participantFields.style.display = 'none';
                document.getElementById('sportMinParticipants').value = '';
                document.getElementById('sportMaxParticipants').value = '';
            }
        }
        
        function editSport(sportId) {
            // Fetch sport data and populate modal
            fetch(`ajax/get-sport.php?id=${sportId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const sport = data.sport;
                        
                        // Update modal title and form
                        document.getElementById('sportModalTitle').textContent = 'Edit Sport';
                        document.getElementById('sportAction').value = 'update';
                        document.getElementById('sportId').value = sport.id;
                        document.getElementById('sportSubmitBtn').textContent = 'Update Sport';
                        
                        // Populate form fields
                        editRecord('sportModal', sport);
                        
                        // Update sport type info if available
                        updateSportTypeInfo();
                    } else {
                        alert('Error loading sport data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading sport data');
                });
        }
        
        function deleteSport(sportId, sportName) {
            if (confirm(`Are you sure you want to delete the sport "${sportName}"? This action cannot be undone.`)) {
                const formData = new FormData();
                formData.append('entity', 'sport');
                formData.append('action', 'delete');
                formData.append('id', sportId);
                
                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modalManager.showSuccessMessage(data.message);
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting sport');
                });
            }
        }

        // Update sport type information display
        function updateSportTypeInfo() {
            const sportTypeSelect = document.getElementById('sportTypeId');
            const sportTypeInfo = document.getElementById('sportTypeInfo');
            const sportTypeDescription = document.getElementById('sportTypeDescription');

            if (sportTypeSelect.value) {
                const selectedOption = sportTypeSelect.options[sportTypeSelect.selectedIndex];
                const description = selectedOption.getAttribute('data-description');
                const category = selectedOption.getAttribute('data-category');

                if (description) {
                    sportTypeDescription.textContent = description;
                    sportTypeInfo.style.display = 'block';
                } else {
                    sportTypeInfo.style.display = 'none';
                }

                // Update scoring method suggestions based on category
                updateScoringMethodSuggestions(category);
            } else {
                sportTypeInfo.style.display = 'none';
            }
        }

        // Update scoring method suggestions based on sport type category
        function updateScoringMethodSuggestions(category) {
            const scoringMethodSelect = document.getElementById('sportScoringMethod');
            const currentValue = scoringMethodSelect.value;

            // Reset all options to visible
            Array.from(scoringMethodSelect.options).forEach(option => {
                option.style.display = 'block';
            });

            // Suggest appropriate scoring methods based on category
            if (category === 'judged' || category === 'performance') {
                // For judged competitions, suggest criteria-based scoring
                if (!currentValue) {
                    scoringMethodSelect.value = 'criteria_based';
                }
            } else if (category === 'traditional') {
                // For traditional sports, suggest point-based or time-based
                if (!currentValue) {
                    scoringMethodSelect.value = 'point_based';
                }
            } else if (category === 'academic') {
                // For academic games, suggest point-based
                if (!currentValue) {
                    scoringMethodSelect.value = 'point_based';
                }
            }
        }

        // Reset modal when opening for new sport
        document.addEventListener('DOMContentLoaded', function() {
            const sportModal = document.getElementById('sportModal');
            sportModal.addEventListener('show', function() {
                if (document.getElementById('sportAction').value === 'create') {
                    document.getElementById('sportModalTitle').textContent = 'Add New Sport';
                    document.getElementById('sportSubmitBtn').textContent = 'Create Sport';
                    sportModal.querySelector('form').reset();
                    toggleParticipantFields();
                }
            });
        });
    </script>

    <style>
        /* Enhanced Sport Type Badges */
        .sport-type-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            color: white;
            font-size: 0.85em;
            font-weight: 500;
            margin-bottom: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .sport-type-badge i {
            font-size: 0.9em;
        }

        .sport-type-description {
            display: block;
            color: #6c757d;
            font-size: 0.75em;
            line-height: 1.2;
            margin-top: 2px;
        }

        /* Improved table cell for sport types */
        .table td:nth-child(3) {
            min-width: 200px;
            vertical-align: top;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sport-type-badge {
                font-size: 0.75em;
                padding: 4px 8px;
                gap: 4px;
            }

            .sport-type-description {
                font-size: 0.7em;
            }

            .table td:nth-child(3) {
                min-width: 150px;
            }
        }

        /* Enhanced status badges for fallback */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .status-traditional {
            background-color: #28a745;
            color: white;
        }

        .status-academic {
            background-color: #6f42c1;
            color: white;
        }

        .status-judged {
            background-color: #fd7e14;
            color: white;
        }

        .status-individual {
            background-color: #17a2b8;
            color: white;
        }
    </style>
</body>
</html>
