<?php
/**
 * Sports Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get all sports
$stmt = $conn->prepare("SELECT * FROM sports ORDER BY name ASC");
$stmt->execute();
$sports = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Management - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-trophy"></i>
                        <span>Sports Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Sports Management</h1>
                <p class="page-description">Create, edit, and manage sports and competition types</p>
            </div>

            <!-- Sports Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Sports</h3>
                    <button class="btn-modal-trigger" onclick="openModal('sportModal')">
                        <i class="fas fa-plus"></i>
                        Add New Sport
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($sports)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Type</th>
                                    <th>Scoring System</th>
                                    <th>Participants</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sports as $sport): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($sport['name'] ?? ''); ?></strong></td>
                                    <td><?php echo htmlspecialchars(substr($sport['description'] ?? '', 0, 80)) . (strlen($sport['description'] ?? '') > 80 ? '...' : ''); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $sport['type'] ?? 'individual'; ?>">
                                            <?php echo ucfirst($sport['type'] ?? 'individual'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo ucfirst($sport['scoring_system'] ?? 'points'); ?></td>
                                    <td>
                                        <?php if ($sport['type'] === 'team'): ?>
                                            <?php echo ($sport['min_participants_per_team'] ?? 1) . '-' . ($sport['max_participants_per_team'] ?? 'unlimited'); ?>
                                        <?php else: ?>
                                            Individual
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editSport(<?php echo $sport['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn-delete" onclick="deleteSport(<?php echo $sport['id']; ?>, '<?php echo htmlspecialchars($sport['name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-trophy" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="font-size: 18px; margin-bottom: 8px;">No sports found</p>
                            <p style="margin-bottom: 20px;">Create your first sport to get started</p>
                            <button class="btn-modal-trigger" onclick="openModal('sportModal')">
                                <i class="fas fa-plus"></i>
                                Create First Sport
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sport Modal -->
    <div id="sportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="sportModalTitle">Add New Sport</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="sport">
                <input type="hidden" name="action" value="create" id="sportAction">
                <input type="hidden" name="id" id="sportId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sportName">Sport Name *</label>
                            <input type="text" id="sportName" name="name" class="form-control" required>
                            <div class="error-message">Sport name is required</div>
                        </div>
                        <div class="form-group">
                            <label for="sportType">Type</label>
                            <select id="sportType" name="type" class="form-control" onchange="toggleParticipantFields()">
                                <option value="individual">Individual</option>
                                <option value="team">Team</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sportDescription">Description</label>
                        <textarea id="sportDescription" name="description" class="form-control" rows="3" placeholder="Enter sport description..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sportScoringSystem">Scoring System</label>
                            <select id="sportScoringSystem" name="scoring_system" class="form-control">
                                <option value="points">Points</option>
                                <option value="time">Time</option>
                                <option value="ranking">Ranking</option>
                                <option value="elimination">Elimination</option>
                            </select>
                        </div>
                        <div class="form-group" id="participantFields" style="display: none;">
                            <label>Team Participants</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="number" id="sportMinParticipants" name="min_participants_per_team" class="form-control" placeholder="Min" min="1">
                                <input type="number" id="sportMaxParticipants" name="max_participants_per_team" class="form-control" placeholder="Max" min="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sportRules">Rules & Regulations</label>
                        <textarea id="sportRules" name="rules" class="form-control" rows="4" placeholder="Enter sport rules and regulations..."></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="sportSubmitBtn">Create Sport</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Sport-specific JavaScript functions
        function toggleParticipantFields() {
            const sportType = document.getElementById('sportType').value;
            const participantFields = document.getElementById('participantFields');
            
            if (sportType === 'team') {
                participantFields.style.display = 'block';
            } else {
                participantFields.style.display = 'none';
                document.getElementById('sportMinParticipants').value = '';
                document.getElementById('sportMaxParticipants').value = '';
            }
        }
        
        function editSport(sportId) {
            // Fetch sport data and populate modal
            fetch(`ajax/get-sport.php?id=${sportId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const sport = data.sport;
                        
                        // Update modal title and form
                        document.getElementById('sportModalTitle').textContent = 'Edit Sport';
                        document.getElementById('sportAction').value = 'update';
                        document.getElementById('sportId').value = sport.id;
                        document.getElementById('sportSubmitBtn').textContent = 'Update Sport';
                        
                        // Populate form fields
                        editRecord('sportModal', sport);
                        
                        // Toggle participant fields if needed
                        toggleParticipantFields();
                    } else {
                        alert('Error loading sport data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading sport data');
                });
        }
        
        function deleteSport(sportId, sportName) {
            if (confirm(`Are you sure you want to delete the sport "${sportName}"? This action cannot be undone.`)) {
                const formData = new FormData();
                formData.append('entity', 'sport');
                formData.append('action', 'delete');
                formData.append('id', sportId);
                
                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modalManager.showSuccessMessage(data.message);
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting sport');
                });
            }
        }
        
        // Reset modal when opening for new sport
        document.addEventListener('DOMContentLoaded', function() {
            const sportModal = document.getElementById('sportModal');
            sportModal.addEventListener('show', function() {
                if (document.getElementById('sportAction').value === 'create') {
                    document.getElementById('sportModalTitle').textContent = 'Add New Sport';
                    document.getElementById('sportSubmitBtn').textContent = 'Create Sport';
                    sportModal.querySelector('form').reset();
                    toggleParticipantFields();
                }
            });
        });
    </script>
</body>
</html>
