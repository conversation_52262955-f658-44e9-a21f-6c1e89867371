<?php
/**
 * Comprehensive Event Management Page for SC_IMS Admin Panel
 * Central hub for managing all aspects of a specific event
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? null;
if (!$event_id) {
    header('Location: events.php');
    exit;
}

$event = getEventById($conn, $event_id);
if (!$event) {
    header('Location: events.php');
    exit;
}

// Get event sports with registration counts
$event_sports = getEventSports($conn, $event_id);

// Get all available sports not yet added to this event
$available_sports = getAvailableSports($conn, $event_id);

// Get all departments
$departments = getAllDepartments($conn);

// Get event standings/rankings
$standings = getEventStandings($conn, $event_id);

// Get recent matches for this event
$recent_matches = getEventRecentMatches($conn, $event_id);

// Calculate event progress
$total_sports = count($event_sports);
$completed_sports = 0;
$total_matches = 0;
$completed_matches = 0;

foreach ($event_sports as $sport) {
    $matches = getEventSportMatches($conn, $sport['id']);
    $total_matches += count($matches);
    $sport_completed = true;
    
    foreach ($matches as $match) {
        if ($match['status'] === 'completed') {
            $completed_matches++;
        } else {
            $sport_completed = false;
        }
    }
    
    if ($sport_completed && count($matches) > 0) {
        $completed_sports++;
    }
}

$event_progress = $total_matches > 0 ? round(($completed_matches / $total_matches) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Event: <?php echo htmlspecialchars($event['name']); ?> - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
    
    <style>
        .event-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .event-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .event-meta {
            display: flex;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .meta-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .meta-item i {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .progress-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .section-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .tab-button {
            flex: 1;
            padding: 18px 24px;
            border: none;
            background: transparent;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 60px;
            color: #6c757d;
        }

        .tab-button:hover {
            background: #f8f9fa;
            color: #495057;
            transform: translateY(-1px);
        }

        .tab-button.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #fff;
            border-radius: 2px;
        }

        .tab-button i {
            font-size: 16px;
        }

        .tab-button span {
            font-weight: 600;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design for tabs */
        @media (max-width: 768px) {
            .section-tabs {
                flex-direction: column;
                padding: 0;
                margin: 20px 0;
            }

            .tab-button {
                border-radius: 0;
                border-bottom: 1px solid #e9ecef;
                justify-content: flex-start;
                padding: 16px 20px;
                min-height: auto;
            }

            .tab-button:first-child {
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }

            .tab-button:last-child {
                border-bottom: none;
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
            }

            .tab-button.active::after {
                display: none;
            }

            .tab-content {
                padding: 20px;
                margin: 10px 0;
            }
        }

        @media (max-width: 480px) {
            .tab-button span {
                font-size: 13px;
            }

            .tab-button i {
                font-size: 14px;
            }

            .tab-content {
                padding: 15px;
            }
        }

        /* Keyboard shortcuts info */
        .keyboard-shortcuts-info {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            animation: slideDown 0.3s ease-out;
        }

        .shortcuts-content {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: space-between;
        }

        .shortcuts-content i.fa-keyboard {
            font-size: 18px;
        }

        .shortcuts-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .shortcuts-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .standings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .standings-table th,
        .standings-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .standings-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .rank-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-weight: 700;
            color: white;
        }
        
        .rank-1 { background: #ffd700; color: #333; }
        .rank-2 { background: #c0c0c0; color: #333; }
        .rank-3 { background: #cd7f32; color: white; }
        .rank-other { background: #6c757d; }
        
        .department-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <a href="events.php"><i class="fas fa-calendar-alt"></i> Events</a>
                    </div>
                    <div class="breadcrumb-separator">/</div>
                    <div class="breadcrumb-item">
                        <span>Manage Event</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <span class="admin-name">Welcome, <?php echo htmlspecialchars($current_admin['username']); ?></span>
                <a href="logout.php" class="btn btn-outline">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Event Header -->
            <div class="event-header">
                <h1 class="event-title"><?php echo htmlspecialchars($event['name']); ?></h1>
                <p><?php echo htmlspecialchars($event['description'] ?? 'No description available'); ?></p>
                
                <div class="event-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span><?php echo date('M j, Y', strtotime($event['start_date'])); ?> - <?php echo date('M j, Y', strtotime($event['end_date'])); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span><?php echo htmlspecialchars($event['location'] ?? 'Location TBD'); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-info-circle"></i>
                        <span class="status-badge status-<?php echo $event['status']; ?>">
                            <?php echo ucfirst($event['status']); ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="progress-section">
                <h3><i class="fas fa-chart-line"></i> Event Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $event_progress; ?>%"></div>
                </div>
                <p><?php echo $event_progress; ?>% Complete (<?php echo $completed_matches; ?>/<?php echo $total_matches; ?> matches finished)</p>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_sports; ?></div>
                    <div class="stat-label">Sports</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($departments); ?></div>
                    <div class="stat-label">Departments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_matches; ?></div>
                    <div class="stat-label">Total Matches</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $completed_matches; ?></div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>

            <!-- Section Tabs -->
            <div class="section-tabs">
                <button class="tab-button active" data-tab="standings" onclick="switchTab('standings')"
                        title="View current event standings and rankings (Ctrl+1)">
                    <i class="fas fa-trophy"></i>
                    <span>Standings</span>
                </button>
                <button class="tab-button" data-tab="sports" onclick="switchTab('sports')"
                        title="Manage sports for this event (Ctrl+2)">
                    <i class="fas fa-futbol"></i>
                    <span>Sports Management</span>
                </button>
                <button class="tab-button" data-tab="registrations" onclick="switchTab('registrations')"
                        title="Manage department registrations (Ctrl+3)">
                    <i class="fas fa-users"></i>
                    <span>Registrations</span>
                </button>
                <button class="tab-button" data-tab="matches" onclick="switchTab('matches')"
                        title="View recent completed matches (Ctrl+4)">
                    <i class="fas fa-calendar-check"></i>
                    <span>Recent Matches</span>
                </button>
            </div>

            <!-- IMMEDIATE SCRIPT - NO DELAYS -->
            <script>
                // Define function immediately after buttons
                function switchTab(tabName) {
                    console.log('switchTab called:', tabName);

                    // Hide all tabs
                    var tabs = document.querySelectorAll('.tab-content');
                    for (var i = 0; i < tabs.length; i++) {
                        tabs[i].classList.remove('active');
                    }

                    // Remove active from all buttons
                    var buttons = document.querySelectorAll('.tab-button');
                    for (var i = 0; i < buttons.length; i++) {
                        buttons[i].classList.remove('active');
                    }

                    // Show selected tab
                    var targetTab = document.getElementById(tabName + '-tab');
                    if (targetTab) {
                        targetTab.classList.add('active');
                    }

                    // Activate clicked button
                    var targetButton = document.querySelector('[data-tab="' + tabName + '"]');
                    if (targetButton) {
                        targetButton.classList.add('active');
                    }

                    console.log('Tab switched to:', tabName);
                }

                // Test function
                function testTabs() {
                    console.log('Testing tabs...');
                    switchTab('sports');
                }

                // Add keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey) {
                        switch(e.key) {
                            case '1':
                                e.preventDefault();
                                switchTab('standings');
                                break;
                            case '2':
                                e.preventDefault();
                                switchTab('sports');
                                break;
                            case '3':
                                e.preventDefault();
                                switchTab('registrations');
                                break;
                            case '4':
                                e.preventDefault();
                                switchTab('matches');
                                break;
                        }
                    }
                });

                console.log('Tab functions and keyboard shortcuts defined');
            </script>

            <!-- Debug Test Buttons -->
            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
                <strong>Debug Controls:</strong>
                <button onclick="switchTab('standings')" class="btn btn-sm btn-secondary">Test Standings</button>
                <button onclick="switchTab('sports')" class="btn btn-sm btn-secondary">Test Sports</button>
                <button onclick="switchTab('registrations')" class="btn btn-sm btn-secondary">Test Registrations</button>
                <button onclick="switchTab('matches')" class="btn btn-sm btn-secondary">Test Matches</button>
                <button onclick="testTabs()" class="btn btn-sm btn-primary">Test Function</button>
                <br><br>
                <button onclick="alert('Basic JavaScript works!')" class="btn btn-sm btn-info">Test Basic JS</button>
                <button onclick="console.log('switchTab type:', typeof switchTab)" class="btn btn-sm btn-warning">Check Function</button>
            </div>

            <!-- DEBUG: HTML structure checkpoint -->
            <script>
                console.log('=== HTML STRUCTURE LOADED ===');
                console.log('Tab buttons found:', document.querySelectorAll('.tab-button').length);
                console.log('Tab contents found:', document.querySelectorAll('.tab-content').length);
            </script>

            <!-- Keyboard Shortcuts Info -->
            <div class="keyboard-shortcuts-info" id="keyboardShortcutsInfo" style="display: none;">
                <div class="shortcuts-content">
                    <i class="fas fa-keyboard"></i>
                    <span>Tip: Use Ctrl+1-4 to quickly switch between tabs</span>
                    <button onclick="hideKeyboardShortcuts()" class="shortcuts-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Standings Tab -->
            <div id="standings-tab" class="tab-content active">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3><i class="fas fa-trophy"></i> Current Standings</h3>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-sm btn-success" onclick="exportResults()" title="Export results">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <?php if ($event['status'] === 'completed'): ?>
                            <button class="btn btn-sm btn-warning" onclick="announceWinner()" title="Announce winner">
                                <i class="fas fa-bullhorn"></i> Announce Winner
                            </button>
                        <?php endif; ?>
                        <button class="btn btn-sm btn-outline" onclick="refreshStandings()" title="Refresh standings">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <?php if (!empty($standings) && $event['status'] === 'completed'): ?>
                    <!-- Winner Announcement Section -->
                    <div class="winner-announcement" style="background: linear-gradient(135deg, #ffd700, #ffed4e); padding: 25px; border-radius: 12px; margin-bottom: 25px; text-align: center; box-shadow: 0 8px 32px rgba(255,215,0,0.3);">
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                            <i class="fas fa-crown" style="font-size: 2rem; color: #b8860b;"></i>
                            <h2 style="margin: 0; color: #b8860b; font-weight: 700;">Event Champion</h2>
                            <i class="fas fa-crown" style="font-size: 2rem; color: #b8860b;"></i>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                            <span class="department-color" style="background-color: <?php echo $standings[0]['color_code'] ?? '#6c757d'; ?>; width: 30px; height: 30px;"></span>
                            <div>
                                <h3 style="margin: 0; color: #b8860b; font-size: 1.8rem;"><?php echo htmlspecialchars($standings[0]['department_name']); ?></h3>
                                <p style="margin: 5px 0 0 0; color: #8b7355; font-weight: 600;">
                                    <?php echo $standings[0]['total_points']; ?> points • <?php echo $standings[0]['matches_won']; ?> wins • <?php echo $standings[0]['win_rate']; ?>% win rate
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Podium Section -->
                    <?php if (count($standings) >= 3): ?>
                        <div class="podium-section" style="background: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
                            <h4 style="text-align: center; margin-bottom: 20px; color: #2c3e50;"><i class="fas fa-medal"></i> Top 3 Departments</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; text-align: center;">
                                <?php for ($i = 0; $i < min(3, count($standings)); $i++): ?>
                                    <?php $dept = $standings[$i]; ?>
                                    <div class="podium-place" style="padding: 20px; border-radius: 12px; <?php echo $i === 0 ? 'background: linear-gradient(135deg, #ffd700, #ffed4e);' : ($i === 1 ? 'background: linear-gradient(135deg, #c0c0c0, #e8e8e8);' : 'background: linear-gradient(135deg, #cd7f32, #daa520);'); ?>">
                                        <div style="font-size: 2rem; margin-bottom: 10px;">
                                            <?php echo $i === 0 ? '🥇' : ($i === 1 ? '🥈' : '🥉'); ?>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 10px;">
                                            <span class="department-color" style="background-color: <?php echo $dept['color_code'] ?? '#6c757d'; ?>"></span>
                                            <strong><?php echo htmlspecialchars($dept['abbreviation']); ?></strong>
                                        </div>
                                        <div style="font-size: 1.5rem; font-weight: 700; margin-bottom: 5px;"><?php echo $dept['total_points']; ?></div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">points</div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php elseif (!empty($standings) && $event_progress > 0): ?>
                    <!-- Current Leader Section -->
                    <div class="current-leader" style="background: linear-gradient(135deg, #3498db, #5dade2); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; text-align: center;">
                        <h4 style="margin: 0 0 10px 0;"><i class="fas fa-star"></i> Current Leader</h4>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                            <span class="department-color" style="background-color: <?php echo $standings[0]['color_code'] ?? '#6c757d'; ?>; border: 2px solid white;"></span>
                            <div>
                                <strong style="font-size: 1.2rem;"><?php echo htmlspecialchars($standings[0]['department_name']); ?></strong>
                                <div style="font-size: 0.9rem; opacity: 0.9;"><?php echo $standings[0]['total_points']; ?> points</div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($standings)): ?>
                    <table class="standings-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Department</th>
                                <th>Sports</th>
                                <th>Matches Won</th>
                                <th>Total Matches</th>
                                <th>Points</th>
                                <th>Win Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($standings as $index => $dept): ?>
                                <?php
                                $rank = $index + 1;
                                $win_rate = $dept['total_matches'] > 0 ? round(($dept['matches_won'] / $dept['total_matches']) * 100, 1) : 0;
                                $rank_class = $rank <= 3 ? "rank-$rank" : "rank-other";
                                ?>
                                <tr>
                                    <td>
                                        <span class="rank-badge <?php echo $rank_class; ?>">
                                            <?php echo $rank; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <span class="department-color" style="background-color: <?php echo $dept['color_code'] ?? '#6c757d'; ?>"></span>
                                            <div>
                                                <strong><?php echo htmlspecialchars($dept['department_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($dept['abbreviation']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo $dept['sports_participated']; ?></td>
                                    <td><?php echo $dept['matches_won']; ?></td>
                                    <td><?php echo $dept['total_matches']; ?></td>
                                    <td><strong><?php echo $dept['total_points']; ?></strong></td>
                                    <td><?php echo $win_rate; ?>%</td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-trophy fa-3x text-muted"></i>
                        <h4>No Standings Available</h4>
                        <p>Standings will appear once matches are completed.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sports Management Tab -->
            <div id="sports-tab" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3><i class="fas fa-futbol"></i> Sports Management</h3>
                    <?php if (!empty($available_sports)): ?>
                        <button class="btn-modal-trigger" onclick="openModal('addSportModal')">
                            <i class="fas fa-plus"></i> Add Sport
                        </button>
                    <?php endif; ?>
                </div>

                <?php if (!empty($event_sports)): ?>
                    <div class="sports-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">
                        <?php foreach ($event_sports as $sport): ?>
                            <div class="sport-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
                                <div class="sport-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <h4 style="margin: 0;"><?php echo htmlspecialchars($sport['sport_name']); ?></h4>
                                    <div class="sport-actions">
                                        <a href="event-sports.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-cog"></i> Manage
                                        </a>
                                        <button class="btn btn-sm btn-danger" onclick="removeSport(<?php echo $sport['id']; ?>, '<?php echo htmlspecialchars($sport['sport_name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="sport-stats" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                                    <div class="stat" style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: 700; color: #2c3e50;"><?php echo $sport['registered_teams']; ?></div>
                                        <div style="font-size: 0.8rem; color: #6c757d;">Teams</div>
                                    </div>
                                    <div class="stat" style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: 700; color: #2c3e50;"><?php echo $sport['total_matches']; ?></div>
                                        <div style="font-size: 0.8rem; color: #6c757d;">Matches</div>
                                    </div>
                                    <div class="stat" style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: 700; color: #2c3e50;"><?php echo $sport['max_teams'] ?? '∞'; ?></div>
                                        <div style="font-size: 0.8rem; color: #6c757d;">Max Teams</div>
                                    </div>
                                </div>
                                <?php if ($sport['registration_deadline']): ?>
                                    <div class="sport-deadline" style="padding: 10px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                                        <i class="fas fa-clock"></i>
                                        Registration deadline: <?php echo date('M j, Y g:i A', strtotime($sport['registration_deadline'])); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-futbol fa-3x text-muted"></i>
                        <h4>No Sports Added</h4>
                        <p>Add sports to this event to get started.</p>
                        <?php if (!empty($available_sports)): ?>
                            <button class="btn btn-primary" onclick="openModal('addSportModal')">
                                <i class="fas fa-plus"></i> Add First Sport
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Registrations Tab -->
            <div id="registrations-tab" class="tab-content">
                <h3><i class="fas fa-users"></i> Department Registrations</h3>

                <?php if (!empty($event_sports)): ?>
                    <?php foreach ($event_sports as $sport): ?>
                        <?php $registrations = getRegistrations($conn, $sport['id']); ?>
                        <div class="sport-registrations" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4><?php echo htmlspecialchars($sport['sport_name']); ?></h4>
                                <button class="btn btn-sm btn-primary" onclick="registerDepartment(<?php echo $sport['id']; ?>)">
                                    <i class="fas fa-plus"></i> Register Department
                                </button>
                            </div>

                            <?php if (!empty($registrations)): ?>
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Department</th>
                                            <th>Team Name</th>
                                            <th>Participants</th>
                                            <th>Status</th>
                                            <th>Registered</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($registrations as $reg): ?>
                                            <tr>
                                                <td>
                                                    <div style="display: flex; align-items: center;">
                                                        <span class="department-color" style="background-color: <?php echo $reg['color_code'] ?? '#6c757d'; ?>"></span>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($reg['department_name']); ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($reg['abbreviation']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($reg['team_name'] ?? 'Default Team'); ?></td>
                                                <td>
                                                    <?php
                                                    $participants = json_decode($reg['participants'] ?? '[]', true);
                                                    echo count($participants) . ' participants';
                                                    ?>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $reg['status']; ?>">
                                                        <?php echo ucfirst($reg['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($reg['registration_date'])); ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn btn-sm btn-secondary" onclick="viewRegistration(<?php echo $reg['id']; ?>)">
                                                            <i class="fas fa-eye"></i> View
                                                        </button>
                                                        <button class="btn btn-sm btn-warning" onclick="editRegistration(<?php echo $reg['id']; ?>)">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" onclick="deleteRegistration(<?php echo $reg['id']; ?>)">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php else: ?>
                                <div class="empty-state" style="text-align: center; padding: 40px 20px; color: #6c757d;">
                                    <i class="fas fa-users fa-2x text-muted"></i>
                                    <p>No departments registered for this sport yet.</p>
                                    <button class="btn btn-primary" onclick="registerDepartment(<?php echo $sport['id']; ?>)">
                                        <i class="fas fa-plus"></i> Register First Department
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-users fa-3x text-muted"></i>
                        <h4>No Sports Available</h4>
                        <p>Add sports to this event first to manage registrations.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Matches Tab -->
            <div id="matches-tab" class="tab-content">
                <h3><i class="fas fa-calendar-check"></i> Recent Matches</h3>

                <?php if (!empty($recent_matches)): ?>
                    <div class="matches-list">
                        <?php foreach ($recent_matches as $match): ?>
                            <div class="match-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div class="match-info" style="flex: 1;">
                                        <div class="sport-name" style="font-size: 0.9rem; color: #6c757d; margin-bottom: 5px;">
                                            <?php echo htmlspecialchars($match['sport_name']); ?>
                                        </div>
                                        <div class="teams" style="display: flex; align-items: center; gap: 15px;">
                                            <div class="team" style="display: flex; align-items: center; gap: 8px;">
                                                <span class="department-color" style="background-color: <?php echo $match['team1_color'] ?? '#6c757d'; ?>"></span>
                                                <span style="font-weight: 600;"><?php echo htmlspecialchars($match['team1_name']); ?></span>
                                                <span style="color: #6c757d;">(<?php echo htmlspecialchars($match['team1_abbr']); ?>)</span>
                                            </div>
                                            <span style="color: #6c757d;">vs</span>
                                            <?php if ($match['team2_name']): ?>
                                                <div class="team" style="display: flex; align-items: center; gap: 8px;">
                                                    <span class="department-color" style="background-color: <?php echo $match['team2_color'] ?? '#6c757d'; ?>"></span>
                                                    <span style="font-weight: 600;"><?php echo htmlspecialchars($match['team2_name']); ?></span>
                                                    <span style="color: #6c757d;">(<?php echo htmlspecialchars($match['team2_abbr']); ?>)</span>
                                                </div>
                                            <?php else: ?>
                                                <span style="color: #6c757d; font-style: italic;">BYE</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="match-result" style="text-align: center; margin: 0 20px;">
                                        <?php if ($match['winner_name']): ?>
                                            <div style="font-size: 1.2rem; font-weight: 700; color: #28a745;">
                                                Winner: <?php echo htmlspecialchars($match['winner_abbr']); ?>
                                            </div>
                                            <?php if ($match['team1_score'] !== null && $match['team2_score'] !== null): ?>
                                                <div style="color: #6c757d; margin-top: 5px;">
                                                    <?php echo $match['team1_score']; ?> - <?php echo $match['team2_score']; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <div style="color: #6c757d;">No winner recorded</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="match-time" style="text-align: right; color: #6c757d; font-size: 0.9rem;">
                                        <?php echo date('M j, Y g:i A', strtotime($match['actual_end_time'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <a href="matches.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline">
                            <i class="fas fa-list"></i> View All Matches
                        </a>
                    </div>
                <?php else: ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-calendar-check fa-3x text-muted"></i>
                        <h4>No Completed Matches</h4>
                        <p>Recent match results will appear here once matches are completed.</p>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Add Sport Modal -->
    <div id="addSportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="addSportModalTitle">Add Sport to Event</h3>
                <span class="close" onclick="closeModal('addSportModal')">&times;</span>
            </div>
            <form id="addSportForm" onsubmit="submitAddSport(event)">
                <div class="modal-body">
                    <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">

                    <div class="form-group">
                        <label for="sport_id">Sport:</label>
                        <select name="sport_id" id="sport_id" required>
                            <option value="">Select a sport...</option>
                            <?php foreach ($available_sports as $sport): ?>
                                <option value="<?php echo $sport['id']; ?>">
                                    <?php echo htmlspecialchars($sport['name']); ?>
                                    (<?php echo ucfirst($sport['type']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="bracket_type">Bracket Type:</label>
                        <select name="bracket_type" id="bracket_type" required>
                            <option value="single_elimination">Single Elimination</option>
                            <option value="double_elimination">Double Elimination</option>
                            <option value="round_robin">Round Robin</option>
                            <option value="multi_stage">Multi-Stage</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="max_teams">Maximum Teams (optional):</label>
                        <input type="number" name="max_teams" id="max_teams" min="2" max="32" placeholder="Leave empty for unlimited">
                    </div>

                    <div class="form-group">
                        <label for="registration_deadline">Registration Deadline (optional):</label>
                        <input type="datetime-local" name="registration_deadline" id="registration_deadline">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addSportModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="addSportSubmitBtn">Add Sport</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Register Department Modal -->
    <div id="registerDepartmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="registerDepartmentModalTitle">Register Department</h3>
                <span class="close" onclick="closeModal('registerDepartmentModal')">&times;</span>
            </div>
            <form id="registerDepartmentForm" onsubmit="submitRegisterDepartment(event)">
                <div class="modal-body">
                    <input type="hidden" name="event_sport_id" id="register_event_sport_id">

                    <div class="form-group">
                        <label for="register_department_id">Department:</label>
                        <select name="department_id" id="register_department_id" required>
                            <option value="">Select a department...</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>">
                                    <?php echo htmlspecialchars($dept['name']); ?>
                                    (<?php echo htmlspecialchars($dept['abbreviation']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="team_name">Team Name (optional):</label>
                        <input type="text" name="team_name" id="team_name" placeholder="Leave empty to use department name">
                    </div>

                    <div class="form-group">
                        <label for="participants">Participants (one per line):</label>
                        <textarea name="participants" id="participants" rows="6" placeholder="Enter participant names, one per line..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="register_status">Status:</label>
                        <select name="status" id="register_status" required>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('registerDepartmentModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="registerDepartmentSubmitBtn">Register Department</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Simple tab system already defined above -->

    <script>
        // Make sure showTab is globally available
        window.showTab = function(tabName, clickedButton) {
            console.log('=== showTab called ===', tabName, clickedButton);

            try {
                // Hide all tab contents
                const allContents = document.querySelectorAll('.tab-content');
                console.log('Found tab contents:', allContents.length);
                allContents.forEach(content => {
                    content.classList.remove('active');
                    console.log('Hiding:', content.id);
                });

                // Remove active class from all tab buttons
                const allButtons = document.querySelectorAll('.tab-button');
                console.log('Found tab buttons:', allButtons.length);
                allButtons.forEach(button => {
                    button.classList.remove('active');
                });

                // Show selected tab content
                const targetTab = document.getElementById(tabName + '-tab');
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('SUCCESS: Showing tab:', targetTab.id);
                } else {
                    console.error('ERROR: Tab not found:', tabName + '-tab');
                    return false;
                }

                // Add active class to clicked button
                if (clickedButton) {
                    clickedButton.classList.add('active');
                    console.log('SUCCESS: Activated button');
                } else {
                    // Fallback: find the button by data-tab attribute
                    const button = document.querySelector(`[data-tab="${tabName}"]`);
                    if (button) {
                        button.classList.add('active');
                        console.log('SUCCESS: Found and activated button by data-tab');
                    }
                }

                // Save to localStorage
                localStorage.setItem('activeEventTab', tabName);
                console.log('SUCCESS: Tab switch completed');
                return true;

            } catch (error) {
                console.error('ERROR in showTab:', error);
                return false;
            }
        };

        // Also create a legacy alias
        function showTab(tabName, clickedButton) {
            return window.showTab(tabName, clickedButton);
        }

        console.log('=== showTab function defined ===', typeof window.showTab);

        // Initialize tabs when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM ready, initializing tabs');

            // Restore saved tab or use default
            const savedTab = localStorage.getItem('activeEventTab');
            if (savedTab && document.getElementById(savedTab + '-tab')) {
                console.log('Restoring saved tab:', savedTab);
                const button = document.querySelector(`[data-tab="${savedTab}"]`);
                showTab(savedTab, button);
            } else {
                console.log('Using default tab: standings');
                const button = document.querySelector(`[data-tab="standings"]`);
                if (button) {
                    showTab('standings', button);
                }
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key >= '1' && e.key <= '4') {
                    e.preventDefault();
                    const tabMap = {
                        '1': 'standings',
                        '2': 'sports',
                        '3': 'registrations',
                        '4': 'matches'
                    };
                    const tabName = tabMap[e.key];
                    if (tabName) {
                        const button = document.querySelector(`[data-tab="${tabName}"]`);
                        showTab(tabName, button);
                    }
                }
            });
        });

        // Test functions for debugging - GLOBAL SCOPE
        window.testTabClick = function(tabName) {
            console.log('=== testTabClick called ===', tabName);
            try {
                const button = document.querySelector(`[data-tab="${tabName}"]`);
                console.log('Found button:', button);
                return window.showTab(tabName, button);
            } catch (error) {
                console.error('ERROR in testTabClick:', error);
                return false;
            }
        };

        window.testAllTabs = function() {
            console.log('=== testAllTabs called ===');
            try {
                const tabs = ['standings', 'sports', 'registrations', 'matches'];
                let index = 0;

                function testNext() {
                    if (index < tabs.length) {
                        console.log(`Testing tab ${index + 1}: ${tabs[index]}`);
                        const button = document.querySelector(`[data-tab="${tabs[index]}"]`);
                        window.showTab(tabs[index], button);
                        index++;
                        setTimeout(testNext, 1000);
                    } else {
                        console.log('All tabs tested successfully');
                    }
                }

                testNext();
            } catch (error) {
                console.error('ERROR in testAllTabs:', error);
            }
        };

        console.log('=== Test functions defined ===', typeof window.testTabClick, typeof window.testAllTabs);

        // Test all tabs after page loads
        window.addEventListener('load', function() {
            console.log('Page fully loaded, running tab test...');
            setTimeout(() => {
                console.log('Testing tab functionality...');
                window.testTabClick('standings');
            }, 1000);
        });

        // Keyboard shortcuts helper functions
        function showKeyboardShortcuts() {
            const info = document.getElementById('keyboardShortcutsInfo');
            if (info) {
                info.style.display = 'block';
                setTimeout(() => {
                    hideKeyboardShortcuts();
                }, 5000); // Auto-hide after 5 seconds
            }
        }

        function hideKeyboardShortcuts() {
            const info = document.getElementById('keyboardShortcutsInfo');
            if (info) {
                info.style.display = 'none';
            }
        }

        // Sport management functions
        function removeSport(sportId, sportName) {
            if (confirm(`Are you sure you want to remove "${sportName}" from this event? This will also remove all registrations and matches for this sport.`)) {
                const formData = new FormData();
                formData.append('action', 'remove_sport');
                formData.append('event_sport_id', sportId);

                fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing sport');
                });
            }
        }

        function registerDepartment(eventSportId) {
            document.getElementById('register_event_sport_id').value = eventSportId;
            openModal('registerDepartmentModal');
        }

        function submitAddSport(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            formData.append('action', 'add_sport');

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal('addSportModal');
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding sport');
            });
        }

        function submitRegisterDepartment(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            formData.append('action', 'register_department');

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal('registerDepartmentModal');
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error registering department');
            });
        }

        function viewRegistration(registrationId) {
            // For now, redirect to a detailed view page
            window.open(`registrations.php?id=${registrationId}`, '_blank');
        }

        function editRegistration(registrationId) {
            // For now, redirect to edit page
            window.location.href = `registrations.php?edit=${registrationId}`;
        }

        function deleteRegistration(registrationId) {
            if (confirm('Are you sure you want to delete this registration? This will also remove all associated matches.')) {
                const formData = new FormData();
                formData.append('action', 'delete_registration');
                formData.append('registration_id', registrationId);

                fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting registration');
                });
            }
        }

        // Live standings update functionality
        function updateStandings() {
            if (document.getElementById('standings-tab').classList.contains('active')) {
                fetch(`ajax/get-standings.php?event_id=<?php echo $event_id; ?>`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStandingsTable(data.data.standings);
                        updateProgressSection(data.data.statistics);
                        console.log('Standings updated at:', data.data.last_updated);
                    }
                })
                .catch(error => console.error('Error refreshing standings:', error));
            }
        }

        function updateStandingsTable(standings) {
            const tbody = document.querySelector('.standings-table tbody');
            if (!tbody || !standings) return;

            tbody.innerHTML = '';

            standings.forEach((dept, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <span class="rank-badge ${dept.rank_class}">
                            ${dept.rank}
                        </span>
                    </td>
                    <td>
                        <div style="display: flex; align-items: center;">
                            <span class="department-color" style="background-color: ${dept.color_code || '#6c757d'}"></span>
                            <div>
                                <strong>${dept.department_name}</strong>
                                <br>
                                <small class="text-muted">${dept.abbreviation}</small>
                            </div>
                        </div>
                    </td>
                    <td>${dept.sports_participated}</td>
                    <td>${dept.matches_won}</td>
                    <td>${dept.total_matches}</td>
                    <td><strong>${dept.total_points}</strong></td>
                    <td>${dept.win_rate}%</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateProgressSection(stats) {
            const progressFill = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-section p');

            if (progressFill && stats) {
                progressFill.style.width = stats.progress_percentage + '%';
            }

            if (progressText && stats) {
                progressText.textContent = `${stats.progress_percentage}% Complete (${stats.total_matches_completed}/${stats.total_matches_played} matches finished)`;
            }
        }

        // Auto-refresh standings every 30 seconds
        setInterval(updateStandings, 30000);

        // Manual refresh button functionality
        function refreshStandings() {
            updateStandings();
        }

        // Export results functionality
        function exportResults() {
            const eventId = <?php echo $event_id; ?>;
            const eventName = '<?php echo addslashes($event['name']); ?>';

            // Create export data
            fetch(`ajax/get-standings.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    exportToCSV(data.data, eventName);
                } else {
                    alert('Error fetching data for export');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error exporting results');
            });
        }

        function exportToCSV(data, eventName) {
            const standings = data.standings;
            const stats = data.statistics;

            // Create CSV content
            let csvContent = `Event: ${eventName}\n`;
            csvContent += `Export Date: ${new Date().toLocaleString()}\n`;
            csvContent += `Total Departments: ${stats.total_departments}\n`;
            csvContent += `Matches Completed: ${stats.total_matches_completed}/${stats.total_matches_played}\n`;
            csvContent += `Event Progress: ${stats.progress_percentage}%\n\n`;

            csvContent += "Rank,Department,Abbreviation,Sports Participated,Matches Won,Total Matches,Points,Win Rate\n";

            standings.forEach(dept => {
                csvContent += `${dept.rank},"${dept.department_name}","${dept.abbreviation}",${dept.sports_participated},${dept.matches_won},${dept.total_matches},${dept.total_points},${dept.win_rate}%\n`;
            });

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${eventName.replace(/[^a-z0-9]/gi, '_')}_results_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Winner announcement functionality
        function announceWinner() {
            const eventId = <?php echo $event_id; ?>;

            if (confirm('Are you sure you want to announce the winner? This will mark the event as officially completed.')) {
                fetch('ajax/event-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=announce_winner&event_id=${eventId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error announcing winner');
                });
            }
        }
    </script>



    <!-- Include modal scripts AFTER our tab system -->
    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
