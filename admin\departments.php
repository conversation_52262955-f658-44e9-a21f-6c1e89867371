<?php
/**
 * Department Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get all departments
$stmt = $conn->prepare("SELECT * FROM departments ORDER BY name ASC");
$stmt->execute();
$departments = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Management - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-building"></i>
                        <span>Department Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Department Management</h1>
                <p class="page-description">Create, edit, and manage academic departments and teams</p>
            </div>

            <!-- Departments Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Departments</h3>
                    <button class="btn-modal-trigger" onclick="openModal('departmentModal')">
                        <i class="fas fa-plus"></i>
                        Add New Department
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($departments)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Abbreviation</th>
                                    <th>Description</th>
                                    <th>Head of Department</th>
                                    <th>Contact</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($departments as $department): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($department['name'] ?? ''); ?></strong></td>
                                    <td>
                                        <span class="status-badge status-ongoing">
                                            <?php echo htmlspecialchars($department['abbreviation'] ?? ''); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars(substr($department['description'] ?? '', 0, 80)) . (strlen($department['description'] ?? '') > 80 ? '...' : ''); ?></td>
                                    <td><?php echo htmlspecialchars($department['head_of_department'] ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if (!empty($department['contact_email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($department['contact_email']); ?>">
                                                <?php echo htmlspecialchars($department['contact_email']); ?>
                                            </a>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editDepartment(<?php echo $department['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn-delete" onclick="deleteDepartment(<?php echo $department['id']; ?>, '<?php echo htmlspecialchars($department['name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-building" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="font-size: 18px; margin-bottom: 8px;">No departments found</p>
                            <p style="margin-bottom: 20px;">Create your first department to get started</p>
                            <button class="btn-modal-trigger" onclick="openModal('departmentModal')">
                                <i class="fas fa-plus"></i>
                                Create First Department
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Modal -->
    <div id="departmentModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="departmentModalTitle">Add New Department</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="department">
                <input type="hidden" name="action" value="create" id="departmentAction">
                <input type="hidden" name="id" id="departmentId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentName">Department Name *</label>
                            <input type="text" id="departmentName" name="name" class="form-control" required>
                            <div class="error-message">Department name is required</div>
                        </div>
                        <div class="form-group">
                            <label for="departmentAbbreviation">Abbreviation *</label>
                            <input type="text" id="departmentAbbreviation" name="abbreviation" class="form-control" required maxlength="10" style="text-transform: uppercase;">
                            <div class="error-message">Abbreviation is required</div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="departmentDescription">Description</label>
                        <textarea id="departmentDescription" name="description" class="form-control" rows="3" placeholder="Enter department description..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentHead">Head of Department</label>
                            <input type="text" id="departmentHead" name="head_of_department" class="form-control" placeholder="Enter department head name...">
                        </div>
                        <div class="form-group">
                            <label for="departmentEmail">Contact Email</label>
                            <input type="email" id="departmentEmail" name="contact_email" class="form-control" placeholder="Enter contact email...">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentPhone">Contact Phone</label>
                            <input type="tel" id="departmentPhone" name="contact_phone" class="form-control" placeholder="Enter contact phone...">
                        </div>
                        <div class="form-group">
                            <label for="departmentLocation">Location</label>
                            <input type="text" id="departmentLocation" name="location" class="form-control" placeholder="Enter department location...">
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="departmentSubmitBtn">Create Department</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Department-specific JavaScript functions
        function editDepartment(departmentId) {
            // Fetch department data and populate modal
            fetch(`ajax/get-department.php?id=${departmentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const department = data.department;
                        
                        // Update modal title and form
                        document.getElementById('departmentModalTitle').textContent = 'Edit Department';
                        document.getElementById('departmentAction').value = 'update';
                        document.getElementById('departmentId').value = department.id;
                        document.getElementById('departmentSubmitBtn').textContent = 'Update Department';
                        
                        // Populate form fields
                        editRecord('departmentModal', department);
                    } else {
                        alert('Error loading department data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading department data');
                });
        }
        
        function deleteDepartment(departmentId, departmentName) {
            if (confirm(`Are you sure you want to delete the department "${departmentName}"? This action cannot be undone.`)) {
                const formData = new FormData();
                formData.append('entity', 'department');
                formData.append('action', 'delete');
                formData.append('id', departmentId);
                
                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modalManager.showSuccessMessage(data.message);
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting department');
                });
            }
        }
        
        // Auto-uppercase abbreviation field
        document.addEventListener('DOMContentLoaded', function() {
            const abbreviationField = document.getElementById('departmentAbbreviation');
            abbreviationField.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });
            
            // Reset modal when opening for new department
            const departmentModal = document.getElementById('departmentModal');
            departmentModal.addEventListener('show', function() {
                if (document.getElementById('departmentAction').value === 'create') {
                    document.getElementById('departmentModalTitle').textContent = 'Add New Department';
                    document.getElementById('departmentSubmitBtn').textContent = 'Create Department';
                    departmentModal.querySelector('form').reset();
                }
            });
        });
    </script>
</body>
</html>
