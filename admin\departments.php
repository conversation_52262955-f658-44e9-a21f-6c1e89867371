<?php
/**
 * Department Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'create':
                $result = createDepartment($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update':
                $result = updateDepartment($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'delete':
                $result = deleteDepartment($conn, $_POST['department_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get all departments
$departments = getAllDepartments($conn);

// Get department for editing
$edit_department = null;
if (isset($_GET['edit'])) {
    $edit_department = getDepartmentById($conn, $_GET['edit']);
}

function createDepartment($conn, $data) {
    try {
        $sql = "INSERT INTO departments (name, abbreviation, description, color_code, contact_person, contact_email, contact_phone) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['abbreviation']),
            sanitizeInput($data['description']),
            $data['color_code'],
            sanitizeInput($data['contact_person']),
            sanitizeInput($data['contact_email']),
            sanitizeInput($data['contact_phone'])
        ]);
        
        logActivity('CREATE_DEPARTMENT', 'departments', $conn->lastInsertId());
        return ['success' => true, 'message' => 'Department created successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to create department: ' . $e->getMessage()];
    }
}

function updateDepartment($conn, $data) {
    try {
        $sql = "UPDATE departments SET name = ?, abbreviation = ?, description = ?, color_code = ?, contact_person = ?, contact_email = ?, contact_phone = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['abbreviation']),
            sanitizeInput($data['description']),
            $data['color_code'],
            sanitizeInput($data['contact_person']),
            sanitizeInput($data['contact_email']),
            sanitizeInput($data['contact_phone']),
            $data['department_id']
        ]);
        
        logActivity('UPDATE_DEPARTMENT', 'departments', $data['department_id']);
        return ['success' => true, 'message' => 'Department updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update department: ' . $e->getMessage()];
    }
}

function deleteDepartment($conn, $department_id) {
    try {
        $sql = "DELETE FROM departments WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$department_id]);
        
        logActivity('DELETE_DEPARTMENT', 'departments', $department_id);
        return ['success' => true, 'message' => 'Department deleted successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to delete department: ' . $e->getMessage()];
    }
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .departments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .department-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .departments-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            border: 2px solid #ddd;
            vertical-align: middle;
        }
        
        .department-card {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .department-info h4 {
            margin: 0;
            color: #333;
        }
        
        .department-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .contact-info {
            font-size: 0.8rem;
            color: #666;
        }
        
        .stats-badge {
            background: #f8f9fa;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>Department Management</h1>
                </div>
                <div class="admin-nav">
                    <a href="index.php">Dashboard</a>
                    <a href="../index.php">Public View</a>
                    </div>
            </header>

            <div class="admin-content">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <div class="departments-header">
                    <h2><?php echo $edit_department ? 'Edit Department' : 'Add New Department'; ?></h2>
                    <?php if ($edit_department): ?>
                        <a href="departments.php" class="btn btn-secondary">Cancel Edit</a>
                    <?php endif; ?>
                </div>

                <!-- Department Form -->
                <div class="department-form">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $edit_department ? 'update' : 'create'; ?>">
                        <?php if ($edit_department): ?>
                            <input type="hidden" name="department_id" value="<?php echo $edit_department['id']; ?>">
                        <?php endif; ?>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Department Name *</label>
                                <input type="text" id="name" name="name" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_department['name'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="abbreviation">Abbreviation *</label>
                                <input type="text" id="abbreviation" name="abbreviation" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_department['abbreviation'] ?? ''); ?>" 
                                       maxlength="10" required>
                            </div>
                            <div class="form-group">
                                <label for="color_code">Color Code</label>
                                <input type="color" id="color_code" name="color_code" class="form-control" 
                                       value="<?php echo $edit_department['color_code'] ?? '#007bff'; ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-control" rows="3"><?php echo htmlspecialchars($edit_department['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="contact_person">Contact Person</label>
                                <input type="text" id="contact_person" name="contact_person" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_department['contact_person'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="contact_email">Contact Email</label>
                                <input type="email" id="contact_email" name="contact_email" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_department['contact_email'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="contact_phone">Contact Phone</label>
                                <input type="tel" id="contact_phone" name="contact_phone" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_department['contact_phone'] ?? ''); ?>">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_department ? 'Update Department' : 'Add Department'; ?>
                        </button>
                    </form>
                </div>

                <!-- Departments Table -->
                <div class="departments-table">
                    <div class="card-header">
                        <h3>All Departments</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($departments)): ?>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Department</th>
                                        <th>Contact Information</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($departments as $department): ?>
                                    <tr>
                                        <td>
                                            <div class="department-card">
                                                <div class="color-preview" style="background-color: <?php echo $department['color_code']; ?>"></div>
                                                <div class="department-info">
                                                    <h4><?php echo htmlspecialchars($department['name']); ?></h4>
                                                    <p><strong><?php echo htmlspecialchars($department['abbreviation']); ?></strong></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($department['contact_person']): ?>
                                                <div class="contact-info">
                                                    <strong><?php echo htmlspecialchars($department['contact_person']); ?></strong><br>
                                                    <?php if ($department['contact_email']): ?>
                                                        📧 <?php echo htmlspecialchars($department['contact_email']); ?><br>
                                                    <?php endif; ?>
                                                    <?php if ($department['contact_phone']): ?>
                                                        📞 <?php echo htmlspecialchars($department['contact_phone']); ?>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <span style="color: #999; font-style: italic;">No contact info</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars(substr($department['description'], 0, 100)) . (strlen($department['description']) > 100 ? '...' : ''); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="departments.php?edit=<?php echo $department['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this department?');">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="department_id" value="<?php echo $department['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No departments found. Add your first department above.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
</body>
</html>
