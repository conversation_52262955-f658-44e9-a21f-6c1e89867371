<?php
/**
 * Department Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get all departments
$stmt = $conn->prepare("SELECT * FROM departments ORDER BY name ASC");
$stmt->execute();
$departments = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Management - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-building"></i>
                        <span>Department Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Department Management</h1>
                <p class="page-description">Create, edit, and manage academic departments and teams</p>
            </div>

            <!-- Departments Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Departments</h3>
                    <button class="btn-modal-trigger" onclick="openModal('departmentModal')">
                        <i class="fas fa-plus"></i>
                        Add New Department
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($departments)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Abbreviation</th>
                                    <th>Color</th>
                                    <th>Description</th>
                                    <th>Head of Department</th>
                                    <th>Contact</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($departments as $department): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($department['name'] ?? ''); ?></strong></td>
                                    <td>
                                        <span class="status-badge status-ongoing">
                                            <?php echo htmlspecialchars($department['abbreviation'] ?? ''); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <div style="width: 24px; height: 24px; border-radius: 4px; border: 2px solid #ddd; background-color: <?php echo htmlspecialchars($department['color_code'] ?? '#3498db'); ?>;"></div>
                                            <span style="font-family: monospace; font-size: 12px; color: #666;">
                                                <?php echo htmlspecialchars($department['color_code'] ?? '#3498db'); ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars(substr($department['description'] ?? '', 0, 60)) . (strlen($department['description'] ?? '') > 60 ? '...' : ''); ?></td>
                                    <td><?php echo htmlspecialchars($department['head_of_department'] ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if (!empty($department['contact_email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($department['contact_email']); ?>">
                                                <?php echo htmlspecialchars($department['contact_email']); ?>
                                            </a>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editDepartment(<?php echo $department['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn-delete" onclick="deleteDepartment(<?php echo $department['id']; ?>, '<?php echo htmlspecialchars($department['name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-building" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="font-size: 18px; margin-bottom: 8px;">No departments found</p>
                            <p style="margin-bottom: 20px;">Create your first department to get started</p>
                            <button class="btn-modal-trigger" onclick="openModal('departmentModal')">
                                <i class="fas fa-plus"></i>
                                Create First Department
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Modal -->
    <div id="departmentModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="departmentModalTitle">Add New Department</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="department">
                <input type="hidden" name="action" value="create" id="departmentAction">
                <input type="hidden" name="id" id="departmentId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentName">Department Name *</label>
                            <input type="text" id="departmentName" name="name" class="form-control" required>
                            <div class="error-message">Department name is required</div>
                        </div>
                        <div class="form-group">
                            <label for="departmentAbbreviation">Abbreviation *</label>
                            <input type="text" id="departmentAbbreviation" name="abbreviation" class="form-control" required maxlength="10" style="text-transform: uppercase;">
                            <div class="error-message">Abbreviation is required</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentColor">Department Color *</label>
                            <div class="color-picker-container">
                                <input type="color" id="departmentColor" name="color_code" class="form-control color-input" value="#3498db" required>
                                <div class="color-preview" id="colorPreview"></div>
                                <div class="predefined-colors">
                                    <span class="color-option" data-color="#3498db" style="background-color: #3498db;" title="Blue"></span>
                                    <span class="color-option" data-color="#e74c3c" style="background-color: #e74c3c;" title="Red"></span>
                                    <span class="color-option" data-color="#2ecc71" style="background-color: #2ecc71;" title="Green"></span>
                                    <span class="color-option" data-color="#f39c12" style="background-color: #f39c12;" title="Orange"></span>
                                    <span class="color-option" data-color="#9b59b6" style="background-color: #9b59b6;" title="Purple"></span>
                                    <span class="color-option" data-color="#1abc9c" style="background-color: #1abc9c;" title="Teal"></span>
                                    <span class="color-option" data-color="#34495e" style="background-color: #34495e;" title="Dark Gray"></span>
                                    <span class="color-option" data-color="#e67e22" style="background-color: #e67e22;" title="Carrot"></span>
                                </div>
                            </div>
                            <div class="error-message">Please select a department color</div>
                        </div>
                        <div class="form-group">
                            <label for="departmentDescription">Description</label>
                            <textarea id="departmentDescription" name="description" class="form-control" rows="3" placeholder="Enter department description..."></textarea>
                        </div>
                    </div>

                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentHead">Head of Department</label>
                            <input type="text" id="departmentHead" name="head_of_department" class="form-control" placeholder="Enter department head name...">
                        </div>
                        <div class="form-group">
                            <label for="departmentEmail">Contact Email</label>
                            <input type="email" id="departmentEmail" name="contact_email" class="form-control" placeholder="Enter contact email...">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="departmentPhone">Contact Phone</label>
                            <input type="tel" id="departmentPhone" name="contact_phone" class="form-control" placeholder="Enter contact phone...">
                        </div>
                        <div class="form-group">
                            <label for="departmentLocation">Location</label>
                            <input type="text" id="departmentLocation" name="location" class="form-control" placeholder="Enter department location...">
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="departmentSubmitBtn">Create Department</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Color picker functionality
        function initializeColorPicker() {
            const colorInput = document.getElementById('departmentColor');
            const colorPreview = document.getElementById('colorPreview');
            const colorOptions = document.querySelectorAll('.color-option');

            // Update preview when color input changes
            colorInput.addEventListener('input', function() {
                updateColorPreview(this.value);
            });

            // Handle predefined color selection
            colorOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const color = this.dataset.color;
                    colorInput.value = color;
                    updateColorPreview(color);
                    updateSelectedOption(this);
                });
            });

            // Initialize with default color
            updateColorPreview(colorInput.value);
            updateSelectedOption(document.querySelector(`[data-color="${colorInput.value}"]`));
        }

        function updateColorPreview(color) {
            const colorPreview = document.getElementById('colorPreview');
            colorPreview.style.backgroundColor = color;
            colorPreview.textContent = color.toUpperCase();
        }

        function updateSelectedOption(selectedOption) {
            // Remove selected class from all options
            document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
            // Add selected class to clicked option
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
        }

        function validateColorFormat(color) {
            const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
            return hexPattern.test(color);
        }



        // Department-specific JavaScript functions
        function editDepartment(departmentId) {
            console.log('editDepartment called with ID:', departmentId);

            // Fetch department data and populate modal
            fetch(`ajax/get-department.php?id=${departmentId}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('Raw response:', text);
                    let data;
                    try {
                        data = JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }

                    if (data.success) {
                        const department = data.department;
                        console.log('Department data:', department);

                        // Update modal title and form
                        document.getElementById('departmentModalTitle').textContent = 'Edit Department';
                        document.getElementById('departmentAction').value = 'update';
                        document.getElementById('departmentId').value = department.id;
                        document.getElementById('departmentSubmitBtn').textContent = 'Update Department';

                        // Populate form fields manually
                        document.getElementById('departmentName').value = department.name || '';
                        document.getElementById('departmentAbbreviation').value = department.abbreviation || '';
                        document.getElementById('departmentDescription').value = department.description || '';
                        document.getElementById('departmentHead').value = department.head_of_department || '';
                        document.getElementById('departmentEmail').value = department.contact_email || '';
                        document.getElementById('departmentPhone').value = department.contact_phone || '';
                        document.getElementById('departmentLocation').value = department.location || '';

                        // Handle color picker specifically
                        const colorInput = document.getElementById('departmentColor');
                        const departmentColor = department.color_code || '#3498db';
                        colorInput.value = departmentColor;
                        updateColorPreview(departmentColor);
                        updateSelectedOption(document.querySelector(`[data-color="${departmentColor}"]`));

                        // Open the modal
                        if (window.modalManager) {
                            window.modalManager.openModal('departmentModal');
                        } else {
                            console.error('ModalManager not available, trying fallback');
                            // Fallback: try to open modal manually
                            const modal = document.getElementById('departmentModal');
                            if (modal) {
                                modal.style.display = 'block';
                                modal.classList.add('show');
                            } else {
                                alert('Modal system not ready. Please refresh the page.');
                            }
                        }
                    } else {
                        console.error('Server error:', data.message);
                        alert('Error loading department data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    alert('Error loading department data: ' + error.message);
                });
        }
        
        function deleteDepartment(departmentId, departmentName) {
            if (confirm(`Are you sure you want to delete the department "${departmentName}"? This action cannot be undone.`)) {
                const formData = new FormData();
                formData.append('entity', 'department');
                formData.append('action', 'delete');
                formData.append('id', departmentId);
                
                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modalManager.showSuccessMessage(data.message);
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting department');
                });
            }
        }

        // Backup form submission handler
        function handleDepartmentFormSubmission(form) {
            console.log('Manual form submission for department');

            const formData = new FormData(form);

            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Manual submission response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Manual submission raw response:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        alert('Department updated successfully!');
                        window.location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                } catch (e) {
                    console.error('JSON parse error:', e);
                    alert('Server error: ' + text.substring(0, 100));
                }
            })
            .catch(error => {
                console.error('Manual submission error:', error);
                alert('Network error: ' + error.message);
            });
        }

        // Auto-uppercase abbreviation field
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded - Initializing department page');
            console.log('jQuery available:', typeof $ !== 'undefined');
            console.log('ModalManager available:', typeof window.modalManager !== 'undefined');

            const abbreviationField = document.getElementById('departmentAbbreviation');
            if (abbreviationField) {
                abbreviationField.addEventListener('input', function() {
                    this.value = this.value.toUpperCase();
                });
            }

            // Reset modal when opening for new department
            const departmentModal = document.getElementById('departmentModal');
            if (departmentModal) {
                departmentModal.addEventListener('show', function() {
                    if (document.getElementById('departmentAction').value === 'create') {
                        document.getElementById('departmentModalTitle').textContent = 'Add New Department';
                        document.getElementById('departmentSubmitBtn').textContent = 'Create Department';
                        departmentModal.querySelector('form').reset();
                        // Reset color picker to default
                        document.getElementById('departmentColor').value = '#3498db';
                        updateColorPreview('#3498db');
                        updateSelectedOption(document.querySelector('[data-color="#3498db"]'));
                    }
                    // Initialize color picker for both create and edit
                    setTimeout(() => {
                        initializeColorPicker();
                    }, 100);
                });
            }

            // Wait for ModalManager to be ready
            const checkModalManager = () => {
                if (window.modalManager) {
                    console.log('ModalManager is ready');
                } else {
                    console.log('ModalManager not ready, checking again...');
                    setTimeout(checkModalManager, 100);
                }
            };
            checkModalManager();

            // Backup form submission handler
            const departmentForm = document.querySelector('#departmentModal .modal-form');
            if (departmentForm) {
                departmentForm.addEventListener('submit', function(e) {
                    console.log('Backup form submission handler triggered');
                    if (!window.modalManager) {
                        console.log('ModalManager not available, handling form submission manually');
                        e.preventDefault();
                        handleDepartmentFormSubmission(this);
                    }
                });
            }
        });
    </script>
</body>
</html>
