<?php
/**
 * Referee Dashboard for SC_IMS
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get upcoming matches for scoring
$assigned_matches = getUpcomingMatches($conn, 50);

// Get recent scoring activity
$recent_scores = getRecentResults($conn, 10);

// Get live matches
$live_matches = getLiveMatches($conn);

function getRefereeMatches($conn, $referee_id) {
    $sql = "SELECT m.*, 
            es.bracket_type,
            s.name as sport_name, s.scoring_system,
            e.name as event_name,
            r1.department_id as team1_dept_id, d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            r2.department_id as team2_dept_id, d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.referee_id = ? OR ? IN (SELECT id FROM users WHERE role = 'admin')
            ORDER BY 
                CASE m.status 
                    WHEN 'live' THEN 1 
                    WHEN 'scheduled' THEN 2 
                    WHEN 'completed' THEN 3 
                    ELSE 4 
                END,
                m.scheduled_time ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$referee_id, $referee_id]);
    return $stmt->fetchAll();
}

function getRecentScores($conn, $referee_id, $limit) {
    $sql = "SELECT sc.*, m.round,
            s.name as sport_name,
            d1.name as team1_name, d1.abbreviation as team1_abbr,
            d2.name as team2_name, d2.abbreviation as team2_abbr
            FROM scores sc
            JOIN matches m ON sc.match_id = m.id
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE sc.referee_id = ?
            ORDER BY sc.created_at DESC
            LIMIT ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$referee_id, $limit]);
    return $stmt->fetchAll();
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referee Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .referee-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .match-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .match-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .match-body {
            padding: 20px;
        }
        
        .teams-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
        }
        
        .team {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .team-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        
        .vs {
            font-weight: bold;
            color: #666;
            font-size: 1.2rem;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-scheduled { background: #fff3cd; color: #856404; }
        .status-live { background: #d4edda; color: #155724; animation: pulse 2s infinite; }
        .status-completed { background: #e2e3e5; color: #383d41; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #dc3545;
            border-radius: 50%;
            animation: blink 1s infinite;
            margin-right: 5px;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>Referee Dashboard</h1>
                </div>
                <div class="admin-nav">
                    <a href="../public/">Public Dashboard</a>
                    <a href="../admin/">Admin Panel</a>
                </div>
            </header>

            <div class="admin-content">
                <div class="referee-header">
                    <h2>Referee Interface</h2>
                    <p>Manage matches and input scores for sports competitions</p>
                </div>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($assigned_matches, fn($m) => $m['status'] === 'scheduled')); ?></div>
                        <div class="stat-label">Scheduled Matches</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($assigned_matches, fn($m) => $m['status'] === 'live')); ?></div>
                        <div class="stat-label">Live Matches</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($assigned_matches, fn($m) => $m['status'] === 'completed')); ?></div>
                        <div class="stat-label">Completed Matches</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($recent_scores); ?></div>
                        <div class="stat-label">Recent Scores</div>
                    </div>
                </div>

                <!-- Live Matches Alert -->
                <?php if (!empty($live_matches)): ?>
                <div class="alert alert-warning">
                    <strong><span class="live-indicator"></span>Live Matches in Progress:</strong>
                    <?php foreach ($live_matches as $live_match): ?>
                        <a href="scoring.php?match_id=<?php echo $live_match['id']; ?>" class="btn btn-sm btn-success" style="margin-left: 10px;">
                            <?php echo htmlspecialchars($live_match['team1_name'] . ' vs ' . $live_match['team2_name']); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Assigned Matches -->
                <div class="card">
                    <div class="card-header">
                        <h3>Your Assigned Matches</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($assigned_matches)): ?>
                            <?php foreach ($assigned_matches as $match): ?>
                                <div class="match-card">
                                    <div class="match-header">
                                        <div>
                                            <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                            <p><?php echo htmlspecialchars($match['event_name']); ?> - Round <?php echo $match['round']; ?></p>
                                        </div>
                                        <div>
                                            <span class="status-badge status-<?php echo $match['status']; ?>">
                                                <?php if ($match['status'] === 'live'): ?>
                                                    <span class="live-indicator"></span>
                                                <?php endif; ?>
                                                <?php echo ucfirst($match['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="match-body">
                                        <div class="teams-display">
                                            <div class="team">
                                                <div class="team-color" style="background-color: <?php echo $match['team1_color']; ?>"></div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($match['team1_name']); ?></strong>
                                                    <br><small><?php echo htmlspecialchars($match['team1_abbr']); ?></small>
                                                </div>
                                            </div>
                                            <div class="vs">VS</div>
                                            <div class="team">
                                                <?php if ($match['team2_name']): ?>
                                                    <div class="team-color" style="background-color: <?php echo $match['team2_color']; ?>"></div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($match['team2_name']); ?></strong>
                                                        <br><small><?php echo htmlspecialchars($match['team2_abbr']); ?></small>
                                                    </div>
                                                <?php else: ?>
                                                    <div style="color: #999; font-style: italic;">BYE</div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <?php if ($match['scheduled_time']): ?>
                                            <p><strong>Scheduled:</strong> <?php echo formatDateTime($match['scheduled_time']); ?></p>
                                        <?php endif; ?>
                                        
                                        <p><strong>Scoring System:</strong> <?php echo ucfirst($match['scoring_system']); ?></p>

                                        <div class="quick-actions">
                                            <?php if ($match['status'] === 'scheduled'): ?>
                                                <a href="scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-primary">Start Match</a>
                                            <?php elseif ($match['status'] === 'live'): ?>
                                                <a href="scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-success">Continue Scoring</a>
                                            <?php else: ?>
                                                <a href="scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-secondary">View Results</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No matches assigned yet.</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Scoring Activity -->
                <?php if (!empty($recent_scores)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3>Recent Scoring Activity</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Match</th>
                                    <th>Teams</th>
                                    <th>Score</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_scores as $score): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($score['sport_name']); ?> - Round <?php echo $score['round']; ?></td>
                                    <td><?php echo htmlspecialchars($score['team1_abbr'] . ' vs ' . $score['team2_abbr']); ?></td>
                                    <td><?php echo htmlspecialchars($score['team1_score'] . ' - ' . $score['team2_score']); ?></td>
                                    <td><?php echo timeAgo($score['created_at']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
    <script>
        // Auto-refresh every 30 seconds for live updates
        setInterval(function() {
            if (document.querySelector('.status-live')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
