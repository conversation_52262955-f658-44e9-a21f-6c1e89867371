<?php
/**
 * Shared Admin JavaScript Component
 * Common JavaScript functionality for all admin pages
 */
?>

<script>
    // Sidebar toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const adminSidebar = document.getElementById('adminSidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const body = document.body;

        function toggleSidebar() {
            adminSidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');
            body.classList.toggle('sidebar-open');
        }

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', toggleSidebar);
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', toggleSidebar);
        }

        // Close sidebar when clicking on nav links on mobile
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 1024) {
                    toggleSidebar();
                }
            });
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1024) {
                adminSidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                body.classList.remove('sidebar-open');
            }
        });
    });

    // Form validation helpers
    function validateForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return false;

        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        return isValid;
    }

    // Show loading state
    function showLoading(element) {
        if (element) {
            element.classList.add('loading');
            element.disabled = true;
        }
    }

    // Hide loading state
    function hideLoading(element) {
        if (element) {
            element.classList.remove('loading');
            element.disabled = false;
        }
    }

    // Show success message
    function showSuccess(message) {
        showNotification(message, 'success');
    }

    // Show error message
    function showError(message) {
        showNotification(message, 'error');
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Confirm deletion
    function confirmDelete(message = 'Are you sure you want to delete this item?') {
        return confirm(message);
    }

    // Auto-save functionality
    function enableAutoSave(formId, saveUrl) {
        const form = document.getElementById(formId);
        if (!form) return;

        const inputs = form.querySelectorAll('input, textarea, select');
        let saveTimeout;

        inputs.forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    autoSave(form, saveUrl);
                }, 2000); // Save after 2 seconds of inactivity
            });
        });
    }

    // Auto-save function
    function autoSave(form, saveUrl) {
        const formData = new FormData(form);
        formData.append('auto_save', '1');

        fetch(saveUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Changes saved automatically', 'success');
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
        });
    }

    // Data table search functionality
    function initializeTableSearch(tableId, searchInputId) {
        const table = document.getElementById(tableId);
        const searchInput = document.getElementById(searchInputId);
        
        if (!table || !searchInput) return;

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }

    // Initialize tooltips
    function initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = this.getAttribute('data-tooltip');
                document.body.appendChild(tooltip);

                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
            });

            element.addEventListener('mouseleave', function() {
                const tooltip = document.querySelector('.tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }

    // Initialize all components when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeTooltips();
    });

    // Modal Management System
    class ModalManager {
        constructor() {
            this.currentModal = null;
            this.init();
        }

        init() {
            // Close modal when clicking outside
            $(document).on('click', '.modal', (e) => {
                if (e.target === e.currentTarget) {
                    this.closeModal();
                }
            });

            // Close modal with escape key
            $(document).on('keydown', (e) => {
                if (e.key === 'Escape' && this.currentModal) {
                    this.closeModal();
                }
            });

            // Close modal with close button
            $(document).on('click', '.modal-close', () => {
                this.closeModal();
            });

            // Handle modal form submissions
            $(document).on('submit', '.modal-form', (e) => {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            });
        }

        openModal(modalId) {
            console.log('ModalManager.openModal called with:', modalId);
            this.closeModal(); // Close any existing modal

            const modal = $(`#${modalId}`);
            console.log('Modal element found:', modal.length > 0);
            if (modal.length) {
                this.currentModal = modal;
                modal.addClass('show');
                $('body').addClass('modal-open');
                console.log('Modal should now be visible');

                // Focus first input
                setTimeout(() => {
                    modal.find('input, select, textarea').first().focus();
                }, 300);
            } else {
                console.error('Modal not found:', modalId);
            }
        }

        closeModal() {
            if (this.currentModal) {
                this.currentModal.removeClass('show');
                $('body').removeClass('modal-open');

                setTimeout(() => {
                    this.currentModal = null;
                }, 300);
            }
        }

        showLoading(modal) {
            modal.find('.modal-loading').addClass('show');
        }

        hideLoading(modal) {
            modal.find('.modal-loading').removeClass('show');
        }

        showError(modal, message) {
            const alertHtml = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
            modal.find('.modal-body').prepend(alertHtml);
        }

        clearErrors(modal) {
            modal.find('.alert').remove();
            modal.find('.error-message').removeClass('show');
            modal.find('.form-control').removeClass('error');
        }

        validateForm(form) {
            let isValid = true;
            const $form = $(form);

            this.clearErrors($form.closest('.modal'));

            // Check required fields
            $form.find('[required]').each(function() {
                const $field = $(this);
                const value = $field.val().trim();

                if (!value) {
                    $field.addClass('error');
                    $field.siblings('.error-message').addClass('show').text('This field is required');
                    isValid = false;
                }
            });

            // Email validation
            $form.find('input[type="email"]').each(function() {
                const $field = $(this);
                const value = $field.val().trim();

                if (value && !this.checkValidity()) {
                    $field.addClass('error');
                    $field.siblings('.error-message').addClass('show').text('Please enter a valid email address');
                    isValid = false;
                }
            });

            return isValid;
        }

        async handleFormSubmit(form) {
            const $form = $(form);
            const modal = $form.closest('.modal');

            if (!this.validateForm(form)) {
                return;
            }

            this.showLoading(modal);

            try {
                const formData = new FormData(form);
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    this.closeModal();
                    this.showSuccessMessage(result.message || 'Operation completed successfully');

                    // Reload page or update table
                    if (result.reload !== false) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                } else {
                    this.hideLoading(modal);
                    this.showError(modal, result.message || 'An error occurred');
                }
            } catch (error) {
                this.hideLoading(modal);
                this.showError(modal, 'Network error occurred. Please try again.');
                console.error('Form submission error:', error);
            }
        }

        showSuccessMessage(message) {
            const alertHtml = `
                <div class="alert alert-success" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-check-circle"></i>
                    ${message}
                </div>
            `;

            $('body').append(alertHtml);

            setTimeout(() => {
                $('.alert-success').fadeOut(() => {
                    $('.alert-success').remove();
                });
            }, 3000);
        }

        populateForm(modalId, data) {
            const modal = $(`#${modalId}`);

            Object.keys(data).forEach(key => {
                const field = modal.find(`[name="${key}"]`);
                if (field.length) {
                    if (field.is('select')) {
                        field.val(data[key]).trigger('change');
                    } else if (field.is(':checkbox')) {
                        field.prop('checked', data[key] == 1);
                    } else {
                        field.val(data[key]);
                    }
                }
            });
        }
    }

    // Initialize modal manager when document is ready
    $(document).ready(function() {
        window.modalManager = new ModalManager();

        // Helper functions for easy modal usage
        window.openModal = function(modalId) {
            console.log('Global openModal called with:', modalId);
            if (window.modalManager) {
                window.modalManager.openModal(modalId);
            } else {
                console.error('ModalManager not initialized');
                alert('Modal system not ready. Please refresh the page.');
            }
        };

        window.closeModal = function() {
            window.modalManager.closeModal();
        };

        window.editRecord = function(modalId, data) {
            window.modalManager.populateForm(modalId, data);
            window.modalManager.openModal(modalId);
        };
    });
</script>

<style>
    /* Additional styles for JavaScript components */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .is-invalid {
        border-color: var(--danger-color) !important;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }

    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    }

    .notification-success {
        border-left: 4px solid var(--success-color);
    }

    .notification-error {
        border-left: 4px solid var(--danger-color);
    }

    .notification-info {
        border-left: 4px solid var(--info-color);
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
    }

    .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        color: var(--text-secondary);
        padding: 4px;
        border-radius: 4px;
        transition: var(--transition);
    }

    .notification-close:hover {
        background: var(--border-color);
        color: var(--text-primary);
    }

    .tooltip {
        position: absolute;
        background: var(--dark-color);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.75rem;
        z-index: 9999;
        pointer-events: none;
        white-space: nowrap;
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
</style>
