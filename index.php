<?php
/**
 * Public Dashboard for SC_IMS
 * Sports Competition and Event Management System
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get current events
$current_events = getAllEvents($conn, 'ongoing');
$upcoming_events = getAllEvents($conn, 'upcoming');

// Get live matches
$live_matches = getLiveMatches($conn);

// Get recent results
$recent_results = getRecentResults($conn, 5);

// Get upcoming matches
$upcoming_matches = getUpcomingMatches($conn, 5);

// Get rankings for current events
$current_rankings = [];
foreach ($current_events as $event) {
    $rankings = getEventRankings($conn, $event['id']);
    if (!empty($rankings)) {
        $current_rankings[$event['id']] = [
            'event' => $event,
            'rankings' => array_slice($rankings, 0, 5) // Top 5
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Live Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo h1 {
            font-size: 2rem;
            font-weight: 700;
        }
        
        .logo p {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .header-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .header-nav a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .header-nav a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .live-indicator {
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #333;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .live-matches {
            grid-column: 1 / -1;
        }
        
        .match-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            transition: background 0.3s;
        }
        
        .match-item:hover {
            background: #f8f9fa;
        }
        
        .match-item:last-child {
            border-bottom: none;
        }
        
        .match-teams {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .team {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .team-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 1px #ddd;
        }
        
        .team-name {
            font-weight: 600;
        }
        
        .vs {
            color: #666;
            font-weight: bold;
        }
        
        .match-info {
            text-align: right;
        }
        
        .match-sport {
            font-size: 0.9rem;
            color: #666;
        }
        
        .match-time {
            font-size: 0.8rem;
            color: #999;
        }
        
        .rankings-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .rankings-table th,
        .rankings-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .rankings-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .rank-position {
            font-weight: bold;
            color: #667eea;
        }
        
        .points {
            font-weight: 600;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            padding: 40px;
            font-style: italic;
        }
        
        .event-selector {
            margin-bottom: 20px;
        }
        
        .event-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .match-teams {
                flex-direction: column;
                gap: 5px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>SC_IMS</h1>
                <p>Sports Competition & Event Management</p>
            </div>
            <nav class="header-nav">
                <?php if (!empty($live_matches)): ?>
                    <span class="live-indicator">● LIVE</span>
                <?php endif; ?>
                <?php if (isLoggedIn()): ?>
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username']); ?></span>
                    <?php if (isAdmin()): ?>
                        <a href="admin/">Admin Panel</a>
                    <?php endif; ?>
                    <?php if (isReferee()): ?>
                        <a href="referee/">Referee Panel</a>
                    <?php endif; ?>
                    <a href="logout.php">Logout</a>
                <?php else: ?>
                    <a href="login.php">Login</a>
                    <a href="register.php">Register</a>
                <?php endif; ?>
            </nav>
        </div>
    </header>

    <div class="container">
        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($current_events); ?></div>
                <div class="stat-label">Ongoing Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($live_matches); ?></div>
                <div class="stat-label">Live Matches</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($upcoming_events); ?></div>
                <div class="stat-label">Upcoming Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($recent_results); ?></div>
                <div class="stat-label">Recent Results</div>
            </div>
        </div>

        <!-- Live Matches -->
        <?php if (!empty($live_matches)): ?>
        <div class="card live-matches">
            <div class="card-header">
                🔴 Live Matches
            </div>
            <div class="card-body">
                <?php foreach ($live_matches as $match): ?>
                <div class="match-item">
                    <div class="match-teams">
                        <div class="team">
                            <div class="team-color" style="background-color: <?php echo $match['team1_color']; ?>"></div>
                            <span class="team-name"><?php echo htmlspecialchars($match['team1_abbr']); ?></span>
                        </div>
                        <span class="vs">VS</span>
                        <?php if ($match['team2_name']): ?>
                        <div class="team">
                            <div class="team-color" style="background-color: <?php echo $match['team2_color']; ?>"></div>
                            <span class="team-name"><?php echo htmlspecialchars($match['team2_abbr']); ?></span>
                        </div>
                        <?php else: ?>
                        <div class="team">
                            <span class="team-name">TBD</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="match-info">
                        <div class="match-sport"><?php echo htmlspecialchars($match['sport_name']); ?></div>
                        <div class="match-time">Started <?php echo timeAgo($match['actual_start_time']); ?></div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="dashboard-grid">
            <!-- Recent Results -->
            <div class="card">
                <div class="card-header">Recent Results</div>
                <div class="card-body">
                    <?php if (!empty($recent_results)): ?>
                        <?php foreach ($recent_results as $match): ?>
                        <div class="match-item">
                            <div class="match-teams">
                                <div class="team">
                                    <div class="team-color" style="background-color: <?php echo $match['team1_color']; ?>"></div>
                                    <span class="team-name"><?php echo htmlspecialchars($match['team1_abbr']); ?></span>
                                    <?php if ($match['winner_abbr'] === $match['team1_abbr']): ?>
                                        <span style="color: #28a745;">✓</span>
                                    <?php endif; ?>
                                </div>
                                <span class="vs">VS</span>
                                <div class="team">
                                    <div class="team-color" style="background-color: <?php echo $match['team2_color']; ?>"></div>
                                    <span class="team-name"><?php echo htmlspecialchars($match['team2_abbr']); ?></span>
                                    <?php if ($match['winner_abbr'] === $match['team2_abbr']): ?>
                                        <span style="color: #28a745;">✓</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="match-info">
                                <div class="match-sport"><?php echo htmlspecialchars($match['sport_name']); ?></div>
                                <div class="match-time"><?php echo timeAgo($match['actual_end_time']); ?></div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-data">No recent results available</div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Current Rankings -->
            <div class="card">
                <div class="card-header">Current Rankings</div>
                <div class="card-body">
                    <?php if (!empty($current_rankings)): ?>
                        <?php foreach ($current_rankings as $event_ranking): ?>
                            <h4 style="margin-bottom: 15px; color: #333;"><?php echo htmlspecialchars($event_ranking['event']['name']); ?></h4>
                            <table class="rankings-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Department</th>
                                        <th>Points</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($event_ranking['rankings'] as $ranking): ?>
                                    <tr>
                                        <td class="rank-position">#<?php echo $ranking['rank_position']; ?></td>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div class="team-color" style="background-color: <?php echo $ranking['color_code']; ?>"></div>
                                                <?php echo htmlspecialchars($ranking['abbreviation']); ?>
                                            </div>
                                        </td>
                                        <td class="points"><?php echo $ranking['total_points']; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            <br>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-data">No rankings available</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Matches -->
        <?php if (!empty($upcoming_matches)): ?>
        <div class="card">
            <div class="card-header">Upcoming Matches</div>
            <div class="card-body">
                <?php foreach ($upcoming_matches as $match): ?>
                <div class="match-item">
                    <div class="match-teams">
                        <div class="team">
                            <div class="team-color" style="background-color: <?php echo $match['team1_color']; ?>"></div>
                            <span class="team-name"><?php echo htmlspecialchars($match['team1_abbr']); ?></span>
                        </div>
                        <span class="vs">VS</span>
                        <?php if ($match['team2_name']): ?>
                        <div class="team">
                            <div class="team-color" style="background-color: <?php echo $match['team2_color']; ?>"></div>
                            <span class="team-name"><?php echo htmlspecialchars($match['team2_abbr']); ?></span>
                        </div>
                        <?php else: ?>
                        <div class="team">
                            <span class="team-name">TBD</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="match-info">
                        <div class="match-sport"><?php echo htmlspecialchars($match['sport_name']); ?></div>
                        <div class="match-time"><?php echo formatDateTime($match['scheduled_time']); ?></div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Server-Sent Events for real-time updates
        let eventSource;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        function initializeSSE() {
            if (typeof(EventSource) !== "undefined") {
                eventSource = new EventSource('api/live-updates.php');

                eventSource.onopen = function(event) {
                    console.log('SSE connection opened');
                    reconnectAttempts = 0;
                    hideConnectionError();
                };

                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleLiveUpdate(data);
                    } catch (e) {
                        console.error('Error parsing SSE data:', e);
                    }
                };

                eventSource.onerror = function(event) {
                    console.error('SSE connection error');
                    eventSource.close();

                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        showConnectionError('Reconnecting... (Attempt ' + reconnectAttempts + ')');
                        setTimeout(initializeSSE, 3000 * reconnectAttempts);
                    } else {
                        showConnectionError('Connection lost. Refreshing page...');
                        setTimeout(() => location.reload(), 5000);
                    }
                };
            } else {
                // Fallback to polling for browsers that don't support SSE
                setInterval(function() {
                    location.reload();
                }, 30000);
            }
        }

        function handleLiveUpdate(data) {
            switch (data.type) {
                case 'score_update':
                    updateMatchScore(data.match_id, data.team1_score, data.team2_score);
                    showUpdateNotification('Score updated: ' + data.team1_name + ' vs ' + data.team2_name);
                    break;
                case 'match_status':
                    updateMatchStatus(data.match_id, data.status);
                    if (data.status === 'live') {
                        showUpdateNotification('Match started: ' + data.team1_name + ' vs ' + data.team2_name);
                    } else if (data.status === 'completed') {
                        showUpdateNotification('Match completed: ' + data.team1_name + ' vs ' + data.team2_name);
                    }
                    break;
                case 'ranking_update':
                    updateRankings(data.rankings);
                    showUpdateNotification('Rankings updated');
                    break;
                case 'new_match':
                    // Refresh page for new matches
                    location.reload();
                    break;
            }
        }

        function updateMatchScore(matchId, team1Score, team2Score) {
            const matchCard = document.querySelector(`[data-match-id="${matchId}"]`);
            if (matchCard) {
                const team1ScoreEl = matchCard.querySelector('.team1-score');
                const team2ScoreEl = matchCard.querySelector('.team2-score');
                if (team1ScoreEl) team1ScoreEl.textContent = team1Score;
                if (team2ScoreEl) team2ScoreEl.textContent = team2Score;

                // Add visual feedback
                matchCard.style.animation = 'scoreUpdate 1s ease-in-out';
                setTimeout(() => {
                    matchCard.style.animation = '';
                }, 1000);
            }
        }

        function updateMatchStatus(matchId, status) {
            const matchCard = document.querySelector(`[data-match-id="${matchId}"]`);
            if (matchCard) {
                const statusEl = matchCard.querySelector('.match-status');
                if (statusEl) {
                    statusEl.textContent = status.toUpperCase();
                    statusEl.className = 'match-status status-' + status;
                }

                if (status === 'completed') {
                    matchCard.classList.add('completed');
                }
            }
        }

        function updateRankings(rankings) {
            const rankingsContainer = document.querySelector('.rankings-list');
            if (rankingsContainer && rankings) {
                // Update rankings display
                rankings.forEach((ranking, index) => {
                    const rankingEl = document.querySelector(`[data-department-id="${ranking.department_id}"]`);
                    if (rankingEl) {
                        const rankEl = rankingEl.querySelector('.rank');
                        const pointsEl = rankingEl.querySelector('.points');
                        if (rankEl) rankEl.textContent = index + 1;
                        if (pointsEl) pointsEl.textContent = ranking.total_points;
                    }
                });
            }
        }

        function showUpdateNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'update-notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        function showConnectionError(message) {
            let errorEl = document.querySelector('.connection-error');
            if (!errorEl) {
                errorEl = document.createElement('div');
                errorEl.className = 'connection-error';
                document.body.appendChild(errorEl);
            }
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }

        function hideConnectionError() {
            const errorEl = document.querySelector('.connection-error');
            if (errorEl) {
                errorEl.style.display = 'none';
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeSSE();

            // Add visual feedback for live indicators
            const liveIndicators = document.querySelectorAll('.live-indicator');
            liveIndicators.forEach(indicator => {
                setInterval(function() {
                    indicator.style.opacity = indicator.style.opacity === '0.5' ? '1' : '0.5';
                }, 1000);
            });
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>

    <style>
        @keyframes scoreUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); background-color: #fff3cd; }
            100% { transform: scale(1); }
        }

        .update-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
            z-index: 1000;
            max-width: 300px;
        }

        .update-notification.show {
            transform: translateX(0);
        }

        .connection-error {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #dc3545;
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 1001;
            display: none;
        }

        .match-card.completed {
            opacity: 0.8;
            border-left: 4px solid #28a745;
        }
    </style>
</body>
</html>
