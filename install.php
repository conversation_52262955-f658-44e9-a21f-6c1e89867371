<?php
/**
 * Installation Script for SC_IMS
 * Sports Competition and Event Management System
 */

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if already installed
if (file_exists('config/.installed')) {
    die('System is already installed. Delete config/.installed file to reinstall.');
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $database = new Database();
        
        // Create database
        if ($database->createDatabase()) {
            $message .= "✓ Database created successfully<br>";
        } else {
            throw new Exception("Failed to create database");
        }
        
        // Initialize tables
        if ($database->initializeTables()) {
            $message .= "✓ Database tables created successfully<br>";
        } else {
            throw new Exception("Failed to create tables");
        }
        
        // Get database connection
        $conn = $database->getConnection();
        
        // Create default admin user
        if (createDefaultAdmin($conn)) {
            $message .= "✓ Default admin user created (username: admin, password: admin123)<br>";
        } else {
            throw new Exception("Failed to create default admin user");
        }
        
        // Initialize sample data
        if (initializeSampleData($conn)) {
            $message .= "✓ Sample data initialized successfully<br>";
        } else {
            throw new Exception("Failed to initialize sample data");
        }
        
        // Create uploads directory
        if (!file_exists('uploads')) {
            mkdir('uploads', 0755, true);
            $message .= "✓ Uploads directory created<br>";
        }
        
        // Create assets directories
        if (!file_exists('assets/css')) {
            mkdir('assets/css', 0755, true);
        }
        if (!file_exists('assets/js')) {
            mkdir('assets/js', 0755, true);
        }
        if (!file_exists('assets/images')) {
            mkdir('assets/images', 0755, true);
        }
        $message .= "✓ Asset directories created<br>";
        
        // Mark as installed
        file_put_contents('config/.installed', date('Y-m-d H:i:s'));
        $message .= "✓ Installation completed successfully!<br><br>";
        $message .= "<strong>Default Login Credentials:</strong><br>";
        $message .= "Username: admin<br>";
        $message .= "Password: admin123<br><br>";
        $message .= "<a href='login.php' class='btn btn-primary'>Go to Login</a>";
        
    } catch (Exception $e) {
        $error = "Installation failed: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SC_IMS Installation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .install-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .install-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .install-form {
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #007bff;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .requirements {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .requirements h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .requirements ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .requirements li {
            padding: 5px 0;
            color: #666;
        }
        
        .requirements li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>SC_IMS</h1>
            <p>Sports Competition & Event Management System</p>
        </div>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php else: ?>
            <div class="requirements">
                <h3>System Requirements</h3>
                <ul>
                    <li>PHP 8.0 or higher</li>
                    <li>MySQL 8.0 or higher</li>
                    <li>PDO MySQL extension</li>
                    <li>Write permissions for uploads directory</li>
                </ul>
            </div>
            
            <div class="install-form">
                <form method="POST">
                    <button type="submit" class="btn">Install SC_IMS</button>
                </form>
            </div>
            
            <p style="color: #666; font-size: 0.9rem;">
                <strong>Note:</strong> This will create the database, tables, and default admin user. 
                Make sure your database credentials are correct in config/database.php
            </p>
        <?php endif; ?>
    </div>
</body>
</html>
