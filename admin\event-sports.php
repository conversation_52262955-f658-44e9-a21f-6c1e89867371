<?php
/**
 * Event Sports Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
// Get database connection
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? null;
if (!$event_id) {
    header('Location: events.php');
    exit;
}

$event = getEventById($conn, $event_id);
if (!$event) {
    header('Location: events.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'add_sport':
                $result = addSportToEvent($conn, $event_id, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update_sport':
                $result = updateEventSport($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'remove_sport':
                $result = removeSportFromEvent($conn, $_POST['event_sport_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'generate_brackets':
                $result = generateBrackets($conn, $_POST['event_sport_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get event sports
$event_sports = getEventSports($conn, $event_id);

// Get available sports not in this event
$available_sports = getAvailableSports($conn, $event_id);

function addSportToEvent($conn, $event_id, $data) {
    try {
        $sql = "INSERT INTO event_sports (event_id, sport_id, bracket_type, max_teams, registration_deadline) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $event_id,
            $data['sport_id'],
            $data['bracket_type'],
            $data['max_teams'] ?: null,
            $data['registration_deadline'] ?: null
        ]);
        
        logActivity('ADD_SPORT_TO_EVENT', 'event_sports', $conn->lastInsertId());
        return ['success' => true, 'message' => 'Sport added to event successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to add sport: ' . $e->getMessage()];
    }
}

function updateEventSport($conn, $data) {
    try {
        $sql = "UPDATE event_sports SET bracket_type = ?, max_teams = ?, registration_deadline = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $data['bracket_type'],
            $data['max_teams'] ?: null,
            $data['registration_deadline'] ?: null,
            $data['event_sport_id']
        ]);
        
        logActivity('UPDATE_EVENT_SPORT', 'event_sports', $data['event_sport_id']);
        return ['success' => true, 'message' => 'Sport configuration updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update sport: ' . $e->getMessage()];
    }
}

function removeSportFromEvent($conn, $event_sport_id) {
    try {
        $sql = "DELETE FROM event_sports WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport_id]);
        
        logActivity('REMOVE_SPORT_FROM_EVENT', 'event_sports', $event_sport_id);
        return ['success' => true, 'message' => 'Sport removed from event successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to remove sport: ' . $e->getMessage()];
    }
}

function generateBrackets($conn, $event_sport_id) {
    try {
        // Get registered teams for this event sport
        $sql = "SELECT r.*, d.name as department_name 
                FROM registrations r 
                JOIN departments d ON r.department_id = d.id 
                WHERE r.event_sport_id = ? AND r.status = 'approved'
                ORDER BY r.created_at";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport_id]);
        $teams = $stmt->fetchAll();
        
        if (count($teams) < 2) {
            return ['success' => false, 'message' => 'At least 2 teams required to generate brackets.'];
        }
        
        // Get event sport details
        $sql = "SELECT * FROM event_sports WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport_id]);
        $event_sport = $stmt->fetch();
        
        // Generate brackets based on type
        $bracket_result = generateBracketMatches($conn, $event_sport, $teams);
        
        if ($bracket_result['success']) {
            logActivity('GENERATE_BRACKETS', 'event_sports', $event_sport_id);
            return ['success' => true, 'message' => 'Brackets generated successfully! ' . $bracket_result['matches_created'] . ' matches created.'];
        } else {
            return ['success' => false, 'message' => $bracket_result['message']];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to generate brackets: ' . $e->getMessage()];
    }
}

function generateBracketMatches($conn, $event_sport, $teams) {
    $bracket_type = $event_sport['bracket_type'];
    $matches_created = 0;
    
    try {
        $conn->beginTransaction();
        
        // Clear existing matches for this event sport
        $sql = "DELETE FROM matches WHERE event_sport_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport['id']]);
        
        switch ($bracket_type) {
            case 'single_elimination':
                $matches_created = generateSingleElimination($conn, $event_sport, $teams);
                break;
            case 'double_elimination':
                $matches_created = generateDoubleElimination($conn, $event_sport, $teams);
                break;
            case 'round_robin':
                $matches_created = generateRoundRobin($conn, $event_sport, $teams);
                break;
            case 'multi_stage':
                $matches_created = generateMultiStage($conn, $event_sport, $teams);
                break;
            default:
                throw new Exception('Invalid bracket type');
        }
        
        $conn->commit();
        return ['success' => true, 'matches_created' => $matches_created];
        
    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

function generateSingleElimination($conn, $event_sport, $teams) {
    $team_count = count($teams);
    $matches_created = 0;
    
    // Calculate number of rounds needed
    $rounds = ceil(log($team_count, 2));
    
    // Shuffle teams for random seeding
    shuffle($teams);
    
    // Create first round matches
    $current_teams = $teams;
    $round = 1;
    
    while (count($current_teams) > 1) {
        $round_matches = [];
        
        for ($i = 0; $i < count($current_teams); $i += 2) {
            if (isset($current_teams[$i + 1])) {
                // Create match between two teams
                $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, round, bracket_position, status) VALUES (?, ?, ?, ?, ?, 'scheduled')";
                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    $event_sport['id'],
                    $current_teams[$i]['id'],
                    $current_teams[$i + 1]['id'],
                    $round,
                    $matches_created + 1
                ]);
                $matches_created++;
            } else {
                // Bye - team advances automatically
                $round_matches[] = $current_teams[$i];
            }
        }
        
        // For next round, we'll need to determine winners
        // This is a placeholder - actual advancement happens when matches are completed
        $current_teams = array_slice($current_teams, 0, ceil(count($current_teams) / 2));
        $round++;
    }
    
    return $matches_created;
}

function generateRoundRobin($conn, $event_sport, $teams) {
    $team_count = count($teams);
    $matches_created = 0;
    
    // Generate all possible pairings
    for ($i = 0; $i < $team_count; $i++) {
        for ($j = $i + 1; $j < $team_count; $j++) {
            $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, round, bracket_position, status) VALUES (?, ?, ?, ?, ?, 'scheduled')";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $event_sport['id'],
                $teams[$i]['id'],
                $teams[$j]['id'],
                1, // All matches in round 1 for round robin
                $matches_created + 1
            ]);
            $matches_created++;
        }
    }
    
    return $matches_created;
}

function generateDoubleElimination($conn, $event_sport, $teams) {
    // Simplified double elimination - create winners and losers brackets
    return generateSingleElimination($conn, $event_sport, $teams) * 2;
}

function generateMultiStage($conn, $event_sport, $teams) {
    // Multi-stage: Group stage + knockout
    $team_count = count($teams);
    
    if ($team_count <= 8) {
        return generateSingleElimination($conn, $event_sport, $teams);
    } else {
        // Create groups and then knockout
        return generateRoundRobin($conn, $event_sport, array_slice($teams, 0, 8));
    }
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Sports - <?php echo htmlspecialchars($event['name']); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .event-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .sport-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .sport-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .sport-body {
            padding: 20px;
        }
        
        .bracket-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #e9ecef;
            color: #495057;
        }
        
        .registration-stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>Event Sports Management</h1>
                </div>
                <div class="admin-nav">
                    <a href="events.php">Back to Events</a>
                    <a href="index.php">Dashboard</a>
                    </div>
            </header>

            <div class="admin-content">
                <div class="event-header">
                    <h2><?php echo htmlspecialchars($event['name']); ?></h2>
                    <p><?php echo htmlspecialchars($event['description']); ?></p>
                    <p><strong>Date:</strong> <?php echo formatDate($event['start_date']); ?> - <?php echo formatDate($event['end_date']); ?></p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <!-- Add Sport Form -->
                <?php if (!empty($available_sports)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3>Add Sport to Event</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="add_sport">

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="sport_id">Sport</label>
                                    <select id="sport_id" name="sport_id" class="form-control" required>
                                        <option value="">Select a sport</option>
                                        <?php foreach ($available_sports as $sport): ?>
                                            <option value="<?php echo $sport['id']; ?>"><?php echo htmlspecialchars($sport['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="bracket_type">Bracket Type</label>
                                    <select id="bracket_type" name="bracket_type" class="form-control">
                                        <option value="single_elimination">Single Elimination</option>
                                        <option value="double_elimination">Double Elimination</option>
                                        <option value="round_robin">Round Robin</option>
                                        <option value="multi_stage">Multi-Stage</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="max_teams">Max Teams</label>
                                    <input type="number" id="max_teams" name="max_teams" class="form-control" min="2">
                                </div>
                                <div class="form-group">
                                    <label for="registration_deadline">Registration Deadline</label>
                                    <input type="datetime-local" id="registration_deadline" name="registration_deadline" class="form-control">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">Add Sport</button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Event Sports List -->
                <div class="card">
                    <div class="card-header">
                        <h3>Sports in this Event</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($event_sports)): ?>
                            <?php foreach ($event_sports as $event_sport): ?>
                                <div class="sport-card">
                                    <div class="sport-header">
                                        <div>
                                            <h4><?php echo htmlspecialchars($event_sport['sport_name']); ?></h4>
                                            <span class="bracket-badge"><?php echo ucfirst(str_replace('_', ' ', $event_sport['bracket_type'])); ?></span>
                                        </div>
                                        <div class="action-buttons">
                                            <a href="registrations.php?event_sport_id=<?php echo $event_sport['id']; ?>" class="btn btn-sm btn-secondary">Manage Registrations</a>
                                            <a href="brackets.php?event_sport_id=<?php echo $event_sport['id']; ?>" class="btn btn-sm btn-primary">View Brackets</a>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="generate_brackets">
                                                <input type="hidden" name="event_sport_id" value="<?php echo $event_sport['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Generate brackets? This will overwrite existing brackets.')">Generate Brackets</button>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="sport-body">
                                        <p><?php echo htmlspecialchars($event_sport['sport_description']); ?></p>
                                        
                                        <div class="registration-stats">
                                            <div class="stat-item">
                                                <div class="stat-number"><?php echo $event_sport['registered_teams'] ?? 0; ?></div>
                                                <div class="stat-label">Registered Teams</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-number"><?php echo $event_sport['max_teams'] ?? 'Unlimited'; ?></div>
                                                <div class="stat-label">Max Teams</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-number"><?php echo $event_sport['total_matches'] ?? 0; ?></div>
                                                <div class="stat-label">Total Matches</div>
                                            </div>
                                        </div>
                                        
                                        <?php if ($event_sport['registration_deadline']): ?>
                                            <p><strong>Registration Deadline:</strong> <?php echo formatDateTime($event_sport['registration_deadline']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No sports added to this event yet.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
</body>
</html>
