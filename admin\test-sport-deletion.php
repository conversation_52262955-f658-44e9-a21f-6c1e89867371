<?php
/**
 * Test script to debug sport deletion and re-addition issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Deletion Test - SC_IMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>Sport Deletion and Re-addition Test</h1>
    
    <?php
    $test_event_id = 1; // Use event ID 1 for testing
    $test_sport_id = 1; // Use sport ID 1 for testing (Basketball)
    
    if ($_POST['action'] ?? '' === 'test_workflow') {
        echo '<div class="test-section info"><h3>Testing Sport Deletion and Re-addition Workflow</h3>';
        
        try {
            // Step 1: Check if sport is currently in event
            echo '<h4>Step 1: Check current state</h4>';
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$test_event_id, $test_sport_id]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                echo "<p>✓ Sport is currently in event (event_sport_id: {$existing['id']})</p>";
                $event_sport_id = $existing['id'];
                
                // Check for related data
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM sport_categories WHERE event_sport_id = ?");
                $stmt->execute([$event_sport_id]);
                $categories_count = $stmt->fetchColumn();
                echo "<p>Categories: {$categories_count}</p>";
                
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM registrations WHERE event_sport_id = ?");
                $stmt->execute([$event_sport_id]);
                $registrations_count = $stmt->fetchColumn();
                echo "<p>Registrations: {$registrations_count}</p>";
                
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE event_sport_id = ?");
                $stmt->execute([$event_sport_id]);
                $matches_count = $stmt->fetchColumn();
                echo "<p>Matches: {$matches_count}</p>";
                
                // Step 2: Remove sport from event
                echo '<h4>Step 2: Remove sport from event</h4>';
                $conn->beginTransaction();
                
                // Delete sport categories (should cascade automatically, but let's be explicit)
                $stmt = $conn->prepare("DELETE FROM sport_categories WHERE event_sport_id = ?");
                $stmt->execute([$event_sport_id]);
                $deleted_categories = $stmt->rowCount();
                echo "<p>Deleted {$deleted_categories} categories</p>";
                
                // Delete matches
                $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = ?");
                $stmt->execute([$event_sport_id]);
                $deleted_matches = $stmt->rowCount();
                echo "<p>Deleted {$deleted_matches} matches</p>";
                
                // Delete registrations
                $stmt = $conn->prepare("DELETE FROM registrations WHERE event_sport_id = ?");
                $stmt->execute([$event_sport_id]);
                $deleted_registrations = $stmt->rowCount();
                echo "<p>Deleted {$deleted_registrations} registrations</p>";
                
                // Delete event sport
                $stmt = $conn->prepare("DELETE FROM event_sports WHERE id = ?");
                $stmt->execute([$event_sport_id]);
                $deleted_event_sports = $stmt->rowCount();
                echo "<p>Deleted {$deleted_event_sports} event_sports record</p>";
                
                $conn->commit();
                echo '<p class="success">✓ Sport removed successfully</p>';
                
            } else {
                echo "<p>Sport is not currently in event</p>";
            }
            
            // Step 3: Verify removal
            echo '<h4>Step 3: Verify removal</h4>';
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$test_event_id, $test_sport_id]);
            $check = $stmt->fetch();
            
            if (!$check) {
                echo '<p class="success">✓ Sport successfully removed from event</p>';
                
                // Step 4: Try to re-add sport
                echo '<h4>Step 4: Re-add sport to event</h4>';
                
                $stmt = $conn->prepare("
                    INSERT INTO event_sports (event_id, sport_id, bracket_type, max_teams, registration_deadline)
                    VALUES (?, ?, 'single_elimination', 8, NULL)
                ");
                $stmt->execute([$test_event_id, $test_sport_id]);
                $new_event_sport_id = $conn->lastInsertId();
                
                echo "<p class=\"success\">✓ Sport re-added successfully (new event_sport_id: {$new_event_sport_id})</p>";
                
            } else {
                echo '<p class="error">✗ Sport still exists in event after deletion attempt</p>';
            }
            
        } catch (Exception $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        
        echo '</div>';
    }
    ?>
    
    <div class="test-section">
        <h3>Current State</h3>
        <?php
        // Show current event_sports for test event
        $stmt = $conn->prepare("
            SELECT es.id, es.event_id, es.sport_id, s.name as sport_name, es.bracket_type, es.max_teams
            FROM event_sports es
            JOIN sports s ON es.sport_id = s.id
            WHERE es.event_id = ?
            ORDER BY s.name
        ");
        $stmt->execute([$test_event_id]);
        $event_sports = $stmt->fetchAll();
        
        if ($event_sports) {
            echo '<table border="1" cellpadding="5" cellspacing="0">';
            echo '<tr><th>Event Sport ID</th><th>Sport ID</th><th>Sport Name</th><th>Bracket Type</th><th>Max Teams</th></tr>';
            foreach ($event_sports as $es) {
                echo '<tr>';
                echo '<td>' . $es['id'] . '</td>';
                echo '<td>' . $es['sport_id'] . '</td>';
                echo '<td>' . htmlspecialchars($es['sport_name']) . '</td>';
                echo '<td>' . $es['bracket_type'] . '</td>';
                echo '<td>' . $es['max_teams'] . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<p>No sports currently in event ' . $test_event_id . '</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h3>Test Actions</h3>
        <form method="POST">
            <input type="hidden" name="action" value="test_workflow">
            <button type="submit" class="btn-primary">Test Complete Workflow</button>
        </form>

        <div style="margin-top: 20px;">
            <h4>AJAX Test</h4>
            <button onclick="testAjaxAddSport()" class="btn-success">Test AJAX Add Sport</button>
            <button onclick="testAjaxRemoveSport()" class="btn-danger">Test AJAX Remove Sport</button>
            <div id="ajaxResults" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; display: none;"></div>
        </div>

        <p><strong>Test Event ID:</strong> <?php echo $test_event_id; ?></p>
        <p><strong>Test Sport ID:</strong> <?php echo $test_sport_id; ?> (Basketball)</p>
        <p><strong>CSRF Token:</strong> <?php echo generateCSRFToken(); ?></p>
    </div>
    
    <div class="test-section">
        <h3>Available Sports</h3>
        <?php
        $stmt = $conn->prepare("SELECT id, name FROM sports ORDER BY name");
        $stmt->execute();
        $sports = $stmt->fetchAll();
        
        echo '<ul>';
        foreach ($sports as $sport) {
            echo '<li>ID: ' . $sport['id'] . ' - ' . htmlspecialchars($sport['name']) . '</li>';
        }
        echo '</ul>';
        ?>
    </div>
    
    <p><a href="manage-event.php?event_id=<?php echo $test_event_id; ?>">← Back to Event Management</a></p>

    <script>
        function showAjaxResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('ajaxResults');
            resultsDiv.style.display = 'block';
            resultsDiv.className = isSuccess ? 'success' : 'error';
            resultsDiv.innerHTML = message;
        }

        function testAjaxAddSport() {
            const formData = new FormData();
            formData.append('action', 'add_sport');
            formData.append('event_id', '<?php echo $test_event_id; ?>');
            formData.append('sport_id', '<?php echo $test_sport_id; ?>');
            formData.append('tournament_format_id', '1'); // Assuming format ID 1 exists
            formData.append('seeding_method', 'random');
            formData.append('max_teams', '8');
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showAjaxResult('Add Sport Result: ' + JSON.stringify(data), data.success);
            })
            .catch(error => {
                showAjaxResult('Add Sport Error: ' + error.message, false);
            });
        }

        function testAjaxRemoveSport() {
            // First, get the event_sport_id
            fetch('ajax/event-management.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_event_sport_id&event_id=<?php echo $test_event_id; ?>&sport_id=<?php echo $test_sport_id; ?>'
            })
            .then(response => response.text())
            .then(text => {
                // For now, let's assume we know the event_sport_id
                // In a real scenario, we'd need to query this first
                const formData = new FormData();
                formData.append('action', 'remove_sport');
                formData.append('event_sport_id', '1'); // This would need to be dynamic
                formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

                return fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
            })
            .then(response => response.json())
            .then(data => {
                showAjaxResult('Remove Sport Result: ' + JSON.stringify(data), data.success);
            })
            .catch(error => {
                showAjaxResult('Remove Sport Error: ' + error.message, false);
            });
        }
    </script>
</body>
</html>
