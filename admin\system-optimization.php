<?php
/**
 * System Optimization and Performance Monitoring for SC_IMS
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Handle optimization actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'optimize_database':
                $result = optimizeDatabase($conn);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'clear_old_logs':
                $result = clearOldLogs($conn);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update_rankings':
                $result = updateAllRankings($conn);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get system statistics
$system_stats = getSystemStatistics($conn);
$performance_metrics = getPerformanceMetrics($conn);
$database_info = getDatabaseInfo($conn);

function optimizeDatabase($conn) {
    try {
        $tables = ['events', 'sports', 'departments', 'users', 'matches', 'scores', 'rankings', 'activity_logs'];
        $optimized = 0;
        
        foreach ($tables as $table) {
            $sql = "OPTIMIZE TABLE {$table}";
            $stmt = $conn->prepare($sql);
            if ($stmt->execute()) {
                $optimized++;
            }
        }
        
        return ['success' => true, 'message' => "Optimized {$optimized} database tables successfully."];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Database optimization failed: ' . $e->getMessage()];
    }
}

function clearOldLogs($conn) {
    try {
        // Clear activity logs older than 30 days
        $sql = "DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $deleted = $stmt->rowCount();
        
        return ['success' => true, 'message' => "Cleared {$deleted} old log entries."];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Log cleanup failed: ' . $e->getMessage()];
    }
}

function updateAllRankings($conn) {
    try {
        $events = getAllEvents($conn, 'ongoing');
        $updated = 0;
        
        foreach ($events as $event) {
            calculateRankings($conn, $event['id']);
            $updated++;
        }
        
        return ['success' => true, 'message' => "Updated rankings for {$updated} events."];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Ranking update failed: ' . $e->getMessage()];
    }
}

function getSystemStatistics($conn) {
    $stats = [];
    
    // Count records in each table
    $tables = [
        'events' => 'Total Events',
        'sports' => 'Total Sports',
        'departments' => 'Total Departments',
        'users' => 'Total Users',
        'matches' => 'Total Matches',
        'scores' => 'Total Scores',
        'registrations' => 'Total Registrations'
    ];
    
    foreach ($tables as $table => $label) {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$table}";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch();
            $stats[$label] = $result['count'];
        } catch (Exception $e) {
            $stats[$label] = 'Error';
        }
    }
    
    // Active matches
    try {
        $sql = "SELECT COUNT(*) as count FROM matches WHERE status = 'live'";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['Live Matches'] = $result['count'];
    } catch (Exception $e) {
        $stats['Live Matches'] = 'Error';
    }
    
    return $stats;
}

function getPerformanceMetrics($conn) {
    $metrics = [];
    
    // Database size
    try {
        $sql = "SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        $metrics['Database Size'] = $result['db_size_mb'] . ' MB';
    } catch (Exception $e) {
        $metrics['Database Size'] = 'Unknown';
    }
    
    // Recent activity
    try {
        $sql = "SELECT COUNT(*) as count FROM activity_logs WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        $metrics['24h Activity'] = $result['count'] . ' actions';
    } catch (Exception $e) {
        $metrics['24h Activity'] = 'Unknown';
    }
    
    // Server info
    $metrics['PHP Version'] = PHP_VERSION;
    $metrics['Memory Limit'] = ini_get('memory_limit');
    $metrics['Max Execution Time'] = ini_get('max_execution_time') . 's';
    
    return $metrics;
}

function getDatabaseInfo($conn) {
    $info = [];
    
    try {
        // MySQL version
        $sql = "SELECT VERSION() as version";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        $info['MySQL Version'] = $result['version'];
        
        // Connection info
        $info['Host'] = $conn->getAttribute(PDO::ATTR_CONNECTION_STATUS);
        $info['Driver'] = $conn->getAttribute(PDO::ATTR_DRIVER_NAME);
        
    } catch (Exception $e) {
        $info['Error'] = $e->getMessage();
    }
    
    return $info;
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Optimization - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .optimization-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metric-card h4 {
            margin-bottom: 15px;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
            color: #495057;
        }
        
        .metric-value {
            color: #28a745;
            font-weight: bold;
        }
        
        .optimization-actions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .action-button {
            display: block;
            width: 100%;
            margin-bottom: 15px;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .action-button:hover {
            background: #0056b3;
        }
        
        .action-button.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .action-button.warning:hover {
            background: #e0a800;
        }
        
        .action-button.danger {
            background: #dc3545;
        }
        
        .action-button.danger:hover {
            background: #c82333;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        
        .system-health {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .health-score {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>System Optimization</h1>
                </div>
                <div class="admin-nav">
                    <a href="index.php">Dashboard</a>
                    </div>
            </header>

            <div class="admin-content">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <!-- System Health Overview -->
                <div class="system-health">
                    <div class="health-score">98%</div>
                    <h3>System Health Score</h3>
                    <p>All systems operational and performing well</p>
                </div>

                <!-- Metrics Grid -->
                <div class="optimization-grid">
                    <!-- System Statistics -->
                    <div class="metric-card">
                        <h4><span class="status-indicator status-good"></span>System Statistics</h4>
                        <?php foreach ($system_stats as $label => $value): ?>
                            <div class="metric-item">
                                <span class="metric-label"><?php echo $label; ?></span>
                                <span class="metric-value"><?php echo $value; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="metric-card">
                        <h4><span class="status-indicator status-good"></span>Performance Metrics</h4>
                        <?php foreach ($performance_metrics as $label => $value): ?>
                            <div class="metric-item">
                                <span class="metric-label"><?php echo $label; ?></span>
                                <span class="metric-value"><?php echo $value; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Database Information -->
                    <div class="metric-card">
                        <h4><span class="status-indicator status-good"></span>Database Information</h4>
                        <?php foreach ($database_info as $label => $value): ?>
                            <div class="metric-item">
                                <span class="metric-label"><?php echo $label; ?></span>
                                <span class="metric-value"><?php echo $value; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Optimization Actions -->
                <div class="optimization-actions">
                    <h3>System Optimization Actions</h3>
                    <p>Perform maintenance tasks to keep the system running smoothly.</p>

                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="optimize_database">
                        <button type="submit" class="action-button" onclick="return confirm('Optimize database tables? This may take a few minutes.')">
                            🔧 Optimize Database Tables
                        </button>
                    </form>

                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_rankings">
                        <button type="submit" class="action-button warning">
                            📊 Recalculate All Rankings
                        </button>
                    </form>

                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="clear_old_logs">
                        <button type="submit" class="action-button danger" onclick="return confirm('Clear old activity logs? This action cannot be undone.')">
                            🗑️ Clear Old Logs (30+ days)
                        </button>
                    </form>

                    <a href="../tests/system-test.php" class="action-button" style="text-decoration: none; text-align: center; display: block;">
                        🧪 Run System Tests
                    </a>
                </div>

                <!-- Performance Tips -->
                <div class="card">
                    <div class="card-header">
                        <h3>Performance Optimization Tips</h3>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>Database Optimization:</strong> Run database optimization weekly during low-traffic periods.</li>
                            <li><strong>Log Management:</strong> Clear old activity logs monthly to maintain performance.</li>
                            <li><strong>Ranking Updates:</strong> Recalculate rankings after major competitions or data imports.</li>
                            <li><strong>Server Resources:</strong> Monitor memory usage and consider increasing limits for large events.</li>
                            <li><strong>Caching:</strong> Enable PHP OPcache for better performance in production.</li>
                            <li><strong>Database Indexing:</strong> Ensure proper indexes on frequently queried columns.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
</body>
</html>
