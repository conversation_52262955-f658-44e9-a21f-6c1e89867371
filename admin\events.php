<?php
/**
 * Event Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'create':
                $result = createEvent($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update':
                $result = updateEvent($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'delete':
                $result = deleteEvent($conn, $_POST['event_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get all events
$events = getAllEvents($conn);

// Get event for editing
$edit_event = null;
if (isset($_GET['edit'])) {
    $edit_event = getEventById($conn, $_GET['edit']);
}

function createEvent($conn, $data) {
    try {
        $sql = "INSERT INTO events (name, description, start_date, end_date, status) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['description']),
            $data['start_date'],
            $data['end_date'],
            $data['status']
        ]);
        
        logActivity('CREATE_EVENT', 'events', $conn->lastInsertId());
        return ['success' => true, 'message' => 'Event created successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to create event: ' . $e->getMessage()];
    }
}

function updateEvent($conn, $data) {
    try {
        $sql = "UPDATE events SET name = ?, description = ?, start_date = ?, end_date = ?, status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['name']),
            sanitizeInput($data['description']),
            $data['start_date'],
            $data['end_date'],
            $data['status'],
            $data['event_id']
        ]);
        
        logActivity('UPDATE_EVENT', 'events', $data['event_id']);
        return ['success' => true, 'message' => 'Event updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update event: ' . $e->getMessage()];
    }
}

function deleteEvent($conn, $event_id) {
    try {
        $sql = "DELETE FROM events WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_id]);
        
        logActivity('DELETE_EVENT', 'events', $event_id);
        return ['success' => true, 'message' => 'Event deleted successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to delete event: ' . $e->getMessage()];
    }
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .events-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .event-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .events-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-upcoming { background: #fff3cd; color: #856404; }
        .status-ongoing { background: #d4edda; color: #155724; }
        .status-completed { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>Event Management</h1>
                </div>
                <div class="admin-nav">
                    <a href="index.php">Dashboard</a>
                    <a href="../index.php">Public View</a>
                </div>
            </header>

            <div class="admin-content">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <div class="events-header">
                    <h2><?php echo $edit_event ? 'Edit Event' : 'Create New Event'; ?></h2>
                    <?php if ($edit_event): ?>
                        <a href="events.php" class="btn btn-secondary">Cancel Edit</a>
                    <?php endif; ?>
                </div>

                <!-- Event Form -->
                <div class="event-form">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $edit_event ? 'update' : 'create'; ?>">
                        <?php if ($edit_event): ?>
                            <input type="hidden" name="event_id" value="<?php echo $edit_event['id']; ?>">
                        <?php endif; ?>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Event Name *</label>
                                <input type="text" id="name" name="name" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_event['name'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="upcoming" <?php echo ($edit_event['status'] ?? '') === 'upcoming' ? 'selected' : ''; ?>>Upcoming</option>
                                    <option value="ongoing" <?php echo ($edit_event['status'] ?? '') === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                    <option value="completed" <?php echo ($edit_event['status'] ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo ($edit_event['status'] ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" class="form-control" rows="3"><?php echo htmlspecialchars($edit_event['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="start_date">Start Date *</label>
                                <input type="date" id="start_date" name="start_date" class="form-control" 
                                       value="<?php echo $edit_event['start_date'] ?? ''; ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="end_date">End Date *</label>
                                <input type="date" id="end_date" name="end_date" class="form-control" 
                                       value="<?php echo $edit_event['end_date'] ?? ''; ?>" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_event ? 'Update Event' : 'Create Event'; ?>
                        </button>
                    </form>
                </div>

                <!-- Events Table -->
                <div class="events-table">
                    <div class="card-header">
                        <h3>All Events</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($events)): ?>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($events as $event): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($event['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars(substr($event['description'], 0, 100)) . (strlen($event['description']) > 100 ? '...' : ''); ?></td>
                                        <td><?php echo formatDate($event['start_date']); ?></td>
                                        <td><?php echo formatDate($event['end_date']); ?></td>
                                        <td>
                                            <span class="status-badge status-<?php echo $event['status']; ?>">
                                                <?php echo ucfirst($event['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="events.php?edit=<?php echo $event['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                <a href="event-sports.php?event_id=<?php echo $event['id']; ?>" class="btn btn-sm btn-secondary">Sports</a>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this event?');">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="event_id" value="<?php echo $event['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No events found. Create your first event above.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
</body>
</html>
