<?php
/**
 * Event Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get all events
$stmt = $conn->prepare("SELECT * FROM events ORDER BY start_date DESC");
$stmt->execute();
$events = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Management - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Event Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Event Management</h1>
                <p class="page-description">Create, edit, and manage sports events and competitions</p>
            </div>

            <!-- Events Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Events</h3>
                    <button class="btn-modal-trigger" onclick="console.log('Button clicked'); openModal('eventModal');">
                        <i class="fas fa-plus"></i>
                        Add New Event
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($events)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($events as $event): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($event['name'] ?? ''); ?></strong></td>
                                    <td><?php echo htmlspecialchars(substr($event['description'] ?? '', 0, 80)) . (strlen($event['description'] ?? '') > 80 ? '...' : ''); ?></td>
                                    <td><?php echo $event['start_date'] ? date('M j, Y', strtotime($event['start_date'])) : 'N/A'; ?></td>
                                    <td><?php echo $event['end_date'] ? date('M j, Y', strtotime($event['end_date'])) : 'N/A'; ?></td>
                                    <td><?php echo htmlspecialchars($event['location'] ?? 'N/A'); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $event['status'] ?? 'upcoming'; ?>">
                                            <?php echo ucfirst($event['status'] ?? 'upcoming'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editEvent(<?php echo $event['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <a href="event-sports.php?event_id=<?php echo $event['id']; ?>" class="btn btn-sm btn-secondary">
                                                <i class="fas fa-link"></i> Sports
                                            </a>
                                            <button class="btn-delete" onclick="deleteEvent(<?php echo $event['id']; ?>, '<?php echo htmlspecialchars($event['name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-calendar-alt" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="font-size: 18px; margin-bottom: 8px;">No events found</p>
                            <p style="margin-bottom: 20px;">Create your first event to get started</p>
                            <button class="btn-modal-trigger" onclick="openModal('eventModal')">
                                <i class="fas fa-plus"></i>
                                Create First Event
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Modal -->
    <div id="eventModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="eventModalTitle">Add New Event</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="event">
                <input type="hidden" name="action" value="create" id="eventAction">
                <input type="hidden" name="id" id="eventId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventName">Event Name *</label>
                            <input type="text" id="eventName" name="name" class="form-control" required>
                            <div class="error-message">Event name is required</div>
                        </div>
                        <div class="form-group">
                            <label for="eventStatus">Status</label>
                            <select id="eventStatus" name="status" class="form-control">
                                <option value="upcoming">Upcoming</option>
                                <option value="ongoing">Ongoing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="eventDescription">Description</label>
                        <textarea id="eventDescription" name="description" class="form-control" rows="3" placeholder="Enter event description..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventStartDate">Start Date *</label>
                            <input type="date" id="eventStartDate" name="start_date" class="form-control" required>
                            <div class="error-message">Start date is required</div>
                        </div>
                        <div class="form-group">
                            <label for="eventEndDate">End Date *</label>
                            <input type="date" id="eventEndDate" name="end_date" class="form-control" required>
                            <div class="error-message">End date is required</div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventLocation">Location</label>
                            <input type="text" id="eventLocation" name="location" class="form-control" placeholder="Enter event location...">
                        </div>
                        <div class="form-group">
                            <label for="eventMaxParticipants">Max Participants per Department</label>
                            <input type="number" id="eventMaxParticipants" name="max_participants_per_department" class="form-control" min="1" placeholder="Optional">
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="eventSubmitBtn">Create Event</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Event-specific JavaScript functions
        function editEvent(eventId) {
            // Fetch event data and populate modal
            fetch(`ajax/get-event.php?id=${eventId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const event = data.event;
                        
                        // Update modal title and form
                        document.getElementById('eventModalTitle').textContent = 'Edit Event';
                        document.getElementById('eventAction').value = 'update';
                        document.getElementById('eventId').value = event.id;
                        document.getElementById('eventSubmitBtn').textContent = 'Update Event';
                        
                        // Populate form fields
                        editRecord('eventModal', event);
                    } else {
                        alert('Error loading event data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading event data');
                });
        }
        
        function deleteEvent(eventId, eventName) {
            if (confirm(`Are you sure you want to delete the event "${eventName}"? This action cannot be undone.`)) {
                const formData = new FormData();
                formData.append('entity', 'event');
                formData.append('action', 'delete');
                formData.append('id', eventId);
                
                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modalManager.showSuccessMessage(data.message);
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting event');
                });
            }
        }
        
        // Reset modal when opening for new event
        document.addEventListener('DOMContentLoaded', function() {
            // Debug: Check if jQuery and modal functions are available
            console.log('jQuery available:', typeof $ !== 'undefined');
            console.log('openModal function available:', typeof window.openModal !== 'undefined');
            console.log('modalManager available:', typeof window.modalManager !== 'undefined');

            // Reset form when opening modal for new event
            const eventModal = document.getElementById('eventModal');
            if (eventModal) {
                eventModal.addEventListener('show', function() {
                    if (document.getElementById('eventAction').value === 'create') {
                        document.getElementById('eventModalTitle').textContent = 'Add New Event';
                        document.getElementById('eventSubmitBtn').textContent = 'Create Event';
                        eventModal.querySelector('form').reset();
                    }
                });
            }
        });
    </script>
</body>
</html>
