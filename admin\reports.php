<?php
/**
 * Reports and Analytics for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Get report data
$reports = [];

try {
    // Overall Statistics
    $sql = "SELECT 
                (SELECT COUNT(*) FROM events WHERE status = 'active') as active_events,
                (SELECT COUNT(*) FROM sports) as total_sports,
                (SELECT COUNT(*) FROM departments) as total_departments,
                (SELECT COUNT(*) FROM registrations) as total_registrations,
                (SELECT COUNT(*) FROM matches WHERE status = 'completed') as completed_matches,
                (SELECT COUNT(*) FROM matches WHERE status = 'live') as live_matches,
                (SELECT COUNT(*) FROM matches WHERE status = 'scheduled') as scheduled_matches";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $reports['overview'] = $stmt->fetch();

    // Department Performance
    $sql = "SELECT 
                d.name as department_name,
                d.abbreviation,
                d.color_code,
                COUNT(DISTINCT r.id) as total_registrations,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' THEN m.id END) as matches_played,
                COALESCE(SUM(CASE WHEN m.status = 'completed' AND m.winner_team_id = r.id THEN 1 ELSE 0 END), 0) as wins,
                COALESCE(SUM(CASE WHEN m.status = 'completed' AND m.winner_team_id != r.id AND (m.team1_id = r.id OR m.team2_id = r.id) THEN 1 ELSE 0 END), 0) as losses,
                COALESCE(AVG(CASE WHEN m.status = 'completed' AND m.team1_id = r.id THEN m.team1_score 
                                  WHEN m.status = 'completed' AND m.team2_id = r.id THEN m.team2_score END), 0) as avg_score
            FROM departments d
            LEFT JOIN registrations r ON d.id = r.department_id
            LEFT JOIN matches m ON (m.team1_id = r.id OR m.team2_id = r.id)
            GROUP BY d.id, d.name, d.abbreviation, d.color_code
            ORDER BY wins DESC, avg_score DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $reports['departments'] = $stmt->fetchAll();

    // Event Statistics
    $sql = "SELECT 
                e.name as event_name,
                e.start_date,
                e.end_date,
                e.status,
                COUNT(DISTINCT es.sport_id) as sports_count,
                COUNT(DISTINCT r.id) as registrations_count,
                COUNT(DISTINCT m.id) as total_matches,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' THEN m.id END) as completed_matches,
                COUNT(DISTINCT CASE WHEN m.status = 'live' THEN m.id END) as live_matches
            FROM events e
            LEFT JOIN event_sports es ON e.id = es.event_id
            LEFT JOIN registrations r ON es.id = r.event_sport_id
            LEFT JOIN matches m ON es.id = m.event_sport_id
            GROUP BY e.id, e.name, e.start_date, e.end_date, e.status
            ORDER BY e.start_date DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $reports['events'] = $stmt->fetchAll();

    // Sport Popularity
    $sql = "SELECT 
                s.name as sport_name,
                s.type as sport_type,
                COUNT(DISTINCT es.event_id) as events_count,
                COUNT(DISTINCT r.id) as registrations_count,
                COUNT(DISTINCT m.id) as matches_count,
                AVG(CASE WHEN m.status = 'completed' THEN (m.team1_score + m.team2_score) END) as avg_total_score
            FROM sports s
            LEFT JOIN event_sports es ON s.id = es.sport_id
            LEFT JOIN registrations r ON es.id = r.event_sport_id
            LEFT JOIN matches m ON es.id = m.event_sport_id
            GROUP BY s.id, s.name, s.type
            ORDER BY registrations_count DESC, matches_count DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $reports['sports'] = $stmt->fetchAll();

    // Recent Activity
    $sql = "SELECT
                al.action as action_type,
                CONCAT(al.action, ' on ', al.table_name) as description,
                al.timestamp as created_at,
                'Admin' as admin_name
            FROM audit_logs al
            ORDER BY al.timestamp DESC
            LIMIT 20";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $reports['recent_activity'] = $stmt->fetchAll();

} catch (Exception $e) {
    $error = "Error loading reports: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports & Analytics - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .report-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-box {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .department-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .department-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .department-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        
        .department-stats {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
        }
        
        .win-rate {
            font-weight: 600;
            color: #28a745;
        }
        
        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-type {
            font-size: 0.8rem;
            color: #007bff;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .activity-description {
            margin-top: 2px;
            color: #666;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #999;
            white-space: nowrap;
            margin-left: 10px;
        }
        
        .export-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .progress-bar {
            background: #f0f0f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .event-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-upcoming {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/admin-sidebar.php'; ?>
        
        <div class="admin-main">
            <?php include '../includes/admin-header.php'; ?>
            
            <div class="admin-content">
                <div class="page-header">
                    <h1 class="page-title">Reports & Analytics</h1>
                    <p class="page-subtitle">Comprehensive insights and performance metrics</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert success"><?php echo htmlspecialchars($message); ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert error"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <!-- Export Options -->
                <div class="export-buttons">
                    <button class="btn btn-primary" onclick="exportReport('overview')">Export Overview</button>
                    <button class="btn btn-secondary" onclick="exportReport('departments')">Export Department Stats</button>
                    <button class="btn btn-secondary" onclick="exportReport('events')">Export Event Stats</button>
                </div>

                <!-- Overview Statistics -->
                <?php if (isset($reports['overview'])): ?>
                <div class="stats-overview">
                    <div class="stat-box">
                        <div class="stat-number"><?php echo $reports['overview']['active_events']; ?></div>
                        <div class="stat-label">Active Events</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number"><?php echo $reports['overview']['total_sports']; ?></div>
                        <div class="stat-label">Sports</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number"><?php echo $reports['overview']['total_departments']; ?></div>
                        <div class="stat-label">Departments</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number"><?php echo $reports['overview']['total_registrations']; ?></div>
                        <div class="stat-label">Registrations</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number"><?php echo $reports['overview']['completed_matches']; ?></div>
                        <div class="stat-label">Completed Matches</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number"><?php echo $reports['overview']['live_matches']; ?></div>
                        <div class="stat-label">Live Matches</div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Detailed Reports -->
                <div class="reports-grid">
                    <!-- Department Performance -->
                    <div class="report-card">
                        <div class="report-header">
                            <h3 class="report-title">Department Performance</h3>
                        </div>
                        <div class="report-content">
                            <?php if (!empty($reports['departments'])): ?>
                                <?php foreach ($reports['departments'] as $dept): ?>
                                    <?php 
                                    $total_matches = $dept['wins'] + $dept['losses'];
                                    $win_rate = $total_matches > 0 ? round(($dept['wins'] / $total_matches) * 100, 1) : 0;
                                    ?>
                                    <div class="department-row">
                                        <div class="department-info">
                                            <div class="department-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($dept['abbreviation']); ?></strong>
                                                <div style="font-size: 0.8rem; color: #666;"><?php echo htmlspecialchars($dept['department_name']); ?></div>
                                            </div>
                                        </div>
                                        <div class="department-stats">
                                            <span class="win-rate"><?php echo $win_rate; ?>%</span>
                                            <span><?php echo $dept['wins']; ?>W</span>
                                            <span><?php echo $dept['losses']; ?>L</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p>No department data available.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Event Statistics -->
                    <div class="report-card">
                        <div class="report-header">
                            <h3 class="report-title">Event Statistics</h3>
                        </div>
                        <div class="report-content">
                            <?php if (!empty($reports['events'])): ?>
                                <?php foreach ($reports['events'] as $event): ?>
                                    <?php 
                                    $completion_rate = $event['total_matches'] > 0 ? 
                                        round(($event['completed_matches'] / $event['total_matches']) * 100, 1) : 0;
                                    ?>
                                    <div class="department-row">
                                        <div>
                                            <strong><?php echo htmlspecialchars($event['event_name']); ?></strong>
                                            <div style="font-size: 0.8rem; color: #666;">
                                                <?php echo formatDate($event['start_date']); ?> - <?php echo formatDate($event['end_date']); ?>
                                            </div>
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?php echo $completion_rate; ?>%"></div>
                                            </div>
                                        </div>
                                        <div style="text-align: right;">
                                            <span class="event-status status-<?php echo $event['status']; ?>">
                                                <?php echo ucfirst($event['status']); ?>
                                            </span>
                                            <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                                                <?php echo $event['completed_matches']; ?>/<?php echo $event['total_matches']; ?> matches
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p>No event data available.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Sport Popularity -->
                    <div class="report-card">
                        <div class="report-header">
                            <h3 class="report-title">Sport Popularity</h3>
                        </div>
                        <div class="report-content">
                            <?php if (!empty($reports['sports'])): ?>
                                <?php foreach ($reports['sports'] as $sport): ?>
                                    <div class="department-row">
                                        <div>
                                            <strong><?php echo htmlspecialchars($sport['sport_name']); ?></strong>
                                            <div style="font-size: 0.8rem; color: #666;">
                                                <?php echo ucfirst($sport['sport_type']); ?>
                                            </div>
                                        </div>
                                        <div style="text-align: right; font-size: 0.9rem;">
                                            <div><?php echo $sport['registrations_count']; ?> teams</div>
                                            <div style="color: #666;"><?php echo $sport['matches_count']; ?> matches</div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p>No sport data available.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="report-card">
                        <div class="report-header">
                            <h3 class="report-title">Recent Activity</h3>
                        </div>
                        <div class="report-content">
                            <?php if (!empty($reports['recent_activity'])): ?>
                                <?php foreach ($reports['recent_activity'] as $activity): ?>
                                    <div class="activity-item">
                                        <div class="activity-content">
                                            <div class="activity-type"><?php echo htmlspecialchars($activity['action_type']); ?></div>
                                            <div class="activity-description"><?php echo htmlspecialchars($activity['description']); ?></div>
                                            <?php if ($activity['admin_name']): ?>
                                                <div style="font-size: 0.8rem; color: #999; margin-top: 2px;">
                                                    by <?php echo htmlspecialchars($activity['admin_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="activity-time">
                                            <?php echo timeAgo($activity['created_at']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p>No recent activity.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function exportReport(type) {
            // This would typically generate and download a CSV/PDF report
            alert('Export functionality for ' + type + ' would be implemented here.');
        }
    </script>
</body>
</html>
