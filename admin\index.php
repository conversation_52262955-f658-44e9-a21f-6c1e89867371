<?php
/**
 * Admin Dashboard for SC_IMS
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get dashboard statistics
$stats = [
    'total_events' => 0,
    'ongoing_events' => 0,
    'total_sports' => 0,
    'total_departments' => 0,
    'live_matches' => 0
];

try {
    // Get event counts
    $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing
            FROM events";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $event_stats = $stmt->fetch();
    $stats['total_events'] = $event_stats['total'];
    $stats['ongoing_events'] = $event_stats['ongoing'];

    // Get sports count
    $sql = "SELECT COUNT(*) as total FROM sports";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['total_sports'] = $stmt->fetch()['total'];

    // Get departments count
    $sql = "SELECT COUNT(*) as total FROM departments";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['total_departments'] = $stmt->fetch()['total'];



    // Get live matches count
    $sql = "SELECT COUNT(*) as total FROM matches WHERE status = 'ongoing'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['live_matches'] = $stmt->fetch()['total'];

} catch (Exception $e) {
    error_log("Error getting dashboard stats: " . $e->getMessage());
}

// Get recent activities from audit logs
$recent_activities = [];
try {
    $sql = "SELECT al.*
            FROM audit_logs al
            ORDER BY al.timestamp DESC
            LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $recent_activities = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting recent activities: " . $e->getMessage());
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --sidebar-bg: #1e293b;
            --sidebar-hover: #334155;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--light-color);
            line-height: 1.6;
            color: var(--text-primary);
            margin-left: 280px;
            transition: margin-left 0.3s ease;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-lg);
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .sidebar-logo-text h1 {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }

        .sidebar-logo-text p {
            font-size: 0.75rem;
            opacity: 0.8;
            margin: 0;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 20px 8px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: rgba(255, 255, 255, 0.6);
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            border-left: 3px solid transparent;
        }

        .nav-link:hover,
        .nav-link.active {
            background: var(--sidebar-hover);
            color: white;
            border-left-color: var(--secondary-color);
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
        }

        .nav-link span {
            font-weight: 500;
        }

        /* Main Content Area */
        .admin-main {
            min-height: 100vh;
            background: var(--light-color);
        }

        .admin-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: var(--transition);
        }

        .sidebar-toggle:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-separator {
            color: var(--border-color);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            background: var(--light-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: var(--transition);
        }

        .user-menu:hover {
            background: white;
            box-shadow: var(--shadow-sm);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .user-role {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        /* Content Area */
        .admin-content {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 32px;
            background: white;
            padding: 24px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-title i {
            color: var(--primary-color);
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
            margin: 0;
        }

        .welcome-section {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 32px;
            border-radius: var(--border-radius);
            margin-bottom: 32px;
            box-shadow: var(--shadow-md);
        }

        .welcome-section h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .welcome-section p {
            opacity: 0.9;
            font-size: 1rem;
            margin: 0;
        }

        /* Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        /* Stat Cards */
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.events {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        }

        .stat-icon.ongoing {
            background: linear-gradient(135deg, var(--success-color), var(--secondary-color));
        }

        .stat-icon.sports {
            background: linear-gradient(135deg, var(--warning-color), #f97316);
        }

        .stat-icon.departments {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .stat-icon.matches {
            background: linear-gradient(135deg, #8b5cf6, #a855f7);
        }

        .stat-content {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Quick Actions Grid */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .action-card {
            background: white;
            padding: 24px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .action-card.events::before {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .action-card.sports::before {
            background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
        }

        .action-card.departments::before {
            background: linear-gradient(90deg, var(--warning-color), #f97316);
        }

        .action-card.matches::before {
            background: linear-gradient(90deg, var(--info-color), #3b82f6);
        }

        .action-card.reports::before {
            background: linear-gradient(90deg, #8b5cf6, #a855f7);
        }

        .action-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .action-card h3 {
            color: var(--text-primary);
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .action-card p {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 0.875rem;
        }

        /* Enhanced Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 600;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--secondary-color);
        }

        .btn-secondary:hover {
            background: #059669;
        }

        .btn-warning {
            background: var(--warning-color);
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-info {
            background: var(--info-color);
        }

        .btn-info:hover {
            background: #2563eb;
        }

        .btn-purple {
            background: #8b5cf6;
        }

        .btn-purple:hover {
            background: #7c3aed;
        }
        
        /* Recent Activities Section */
        .recent-activities {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            background: var(--light-color);
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-header i {
            color: var(--primary-color);
        }

        .card-body {
            padding: 24px;
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-info {
            flex: 1;
        }

        .activity-action {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .activity-user {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--text-secondary);
            background: var(--light-color);
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        /* Responsive Design */
        @media (max-width: 1024px) {
            body {
                margin-left: 0;
            }

            .admin-sidebar {
                transform: translateX(-280px);
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .admin-content {
                padding: 16px;
            }

            .page-header {
                padding: 20px;
                margin-bottom: 24px;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .welcome-section {
                padding: 24px;
                margin-bottom: 24px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .quick-actions {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .action-card {
                padding: 20px;
            }

            .user-menu {
                padding: 6px 8px;
            }

            .user-info {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .admin-content {
                padding: 12px;
            }

            .page-header {
                padding: 16px;
            }

            .welcome-section {
                padding: 20px;
            }

            .stat-card {
                padding: 20px;
            }

            .action-card {
                padding: 16px;
            }
        }

        /* Sidebar Toggle Animation */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Admin Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <div class="sidebar-logo-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="sidebar-logo-text">
                    <h1>SC_IMS</h1>
                    <p>Admin Panel</p>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="index.php" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <div class="nav-item">
                    <a href="events.php" class="nav-link">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Events</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="sports.php" class="nav-link">
                        <i class="fas fa-futbol"></i>
                        <span>Sports</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="departments.php" class="nav-link">
                        <i class="fas fa-building"></i>
                        <span>Departments</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="matches.php" class="nav-link">
                        <i class="fas fa-trophy"></i>
                        <span>Matches</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Analytics</div>
                <div class="nav-item">
                    <a href="reports.php" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="system-optimization.php" class="nav-link">
                        <i class="fas fa-cogs"></i>
                        <span>System</span>
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        <span>Dashboard</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-menu">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($current_admin['full_name'] ?? $current_admin['username'], 0, 1)); ?>
                    </div>
                    <div class="user-info">
                        <div class="user-name"><?php echo htmlspecialchars($current_admin['full_name'] ?? $current_admin['username']); ?></div>
                        <div class="user-role">Administrator</div>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <a href="logout.php" class="btn btn-secondary" style="margin-left: 12px;">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt"></i>
                    Admin Dashboard
                </h1>
                <p class="page-subtitle">Manage your sports competition and event management system</p>
            </div>

            <div class="welcome-section">
                <h2>Welcome to SC_IMS Admin Panel</h2>
                <p>From here you can manage events, sports, departments, and monitor all system activities. Use the quick actions below to get started.</p>
            </div>

            <!-- Statistics Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon events">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $stats['total_events']; ?></div>
                        <div class="stat-label">Total Events</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon ongoing">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $stats['ongoing_events']; ?></div>
                        <div class="stat-label">Ongoing Events</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon sports">
                            <i class="fas fa-futbol"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $stats['total_sports']; ?></div>
                        <div class="stat-label">Sports Available</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon departments">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $stats['total_departments']; ?></div>
                        <div class="stat-label">Departments</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon matches">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo $stats['live_matches']; ?></div>
                        <div class="stat-label">Live Matches</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="action-card events">
                    <div class="action-header">
                        <div class="action-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--info-color));">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3>Event Management</h3>
                    </div>
                    <p>Create, edit, and manage sports events and competitions</p>
                    <a href="events.php" class="btn">
                        <i class="fas fa-cog"></i>
                        Manage Events
                    </a>
                </div>
                <div class="action-card sports">
                    <div class="action-header">
                        <div class="action-icon" style="background: linear-gradient(135deg, var(--secondary-color), var(--success-color));">
                            <i class="fas fa-futbol"></i>
                        </div>
                        <h3>Sports Configuration</h3>
                    </div>
                    <p>Add and configure different sports and competition types</p>
                    <a href="sports.php" class="btn btn-secondary">
                        <i class="fas fa-plus"></i>
                        Manage Sports
                    </a>
                </div>
                <div class="action-card departments">
                    <div class="action-header">
                        <div class="action-icon" style="background: linear-gradient(135deg, var(--warning-color), #f97316);">
                            <i class="fas fa-building"></i>
                        </div>
                        <h3>Department Management</h3>
                    </div>
                    <p>Manage participating departments and teams</p>
                    <a href="departments.php" class="btn btn-warning">
                        <i class="fas fa-users"></i>
                        Manage Departments
                    </a>
                </div>
                <div class="action-card matches">
                    <div class="action-header">
                        <div class="action-icon" style="background: linear-gradient(135deg, var(--info-color), #3b82f6);">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h3>Match Management</h3>
                    </div>
                    <p>View and manage ongoing matches and brackets</p>
                    <a href="matches.php" class="btn btn-info">
                        <i class="fas fa-play"></i>
                        Manage Matches
                    </a>
                </div>
                <div class="action-card reports">
                    <div class="action-header">
                        <div class="action-icon" style="background: linear-gradient(135deg, #8b5cf6, #a855f7);">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3>Reports & Analytics</h3>
                    </div>
                    <p>Generate reports and view system analytics</p>
                    <a href="reports.php" class="btn btn-purple">
                        <i class="fas fa-analytics"></i>
                        View Reports
                    </a>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="recent-activities">
                <div class="card-header">
                    <i class="fas fa-clock"></i>
                    Recent System Activities
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_activities)): ?>
                        <?php foreach ($recent_activities as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-info">
                                <div class="activity-action"><?php echo htmlspecialchars($activity['action']); ?></div>
                                <div class="activity-user">
                                    by <?php echo htmlspecialchars($activity['full_name'] ?? $activity['username'] ?? 'System'); ?>
                                </div>
                            </div>
                            <div class="activity-time"><?php echo timeAgo($activity['timestamp']); ?></div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <i class="fas fa-history" style="font-size: 3rem; margin-bottom: 16px; opacity: 0.3;"></i>
                            <p style="margin: 0; font-style: italic;">No recent activities</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sidebar toggle functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const adminSidebar = document.getElementById('adminSidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const body = document.body;

        function toggleSidebar() {
            adminSidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');
            body.classList.toggle('sidebar-open');
        }

        sidebarToggle.addEventListener('click', toggleSidebar);
        sidebarOverlay.addEventListener('click', toggleSidebar);

        // Close sidebar when clicking on nav links on mobile
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 1024) {
                    toggleSidebar();
                }
            });
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1024) {
                adminSidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
                body.classList.remove('sidebar-open');
            }
        });

        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            // Only refresh if page is visible
            if (!document.hidden) {
                fetch(window.location.href)
                    .then(response => response.text())
                    .then(html => {
                        const parser = new DOMParser();
                        const newDoc = parser.parseFromString(html, 'text/html');

                        // Update stat numbers
                        const statCards = document.querySelectorAll('.stat-number');
                        const newStatCards = newDoc.querySelectorAll('.stat-number');

                        statCards.forEach((card, index) => {
                            if (newStatCards[index] && card.textContent !== newStatCards[index].textContent) {
                                card.style.transform = 'scale(1.1)';
                                card.textContent = newStatCards[index].textContent;
                                setTimeout(() => {
                                    card.style.transform = 'scale(1)';
                                }, 200);
                            }
                        });
                    })
                    .catch(error => console.log('Auto-refresh failed:', error));
            }
        }, 30000);

        // Add loading states to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.href && !this.href.includes('#')) {
                    this.style.opacity = '0.7';
                    this.style.pointerEvents = 'none';

                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

                    // Reset after 3 seconds if page doesn't change
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.opacity = '1';
                        this.style.pointerEvents = 'auto';
                    }, 3000);
                }
            });
        });
    </script>
</body>
</html>
