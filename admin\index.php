<?php
/**
 * Admin Dashboard for SC_IMS
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get dashboard statistics
$stats = [
    'total_events' => 0,
    'ongoing_events' => 0,
    'total_sports' => 0,
    'total_departments' => 0,
    'total_users' => 0,
    'live_matches' => 0
];

try {
    // Get event counts
    $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing
            FROM events";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $event_stats = $stmt->fetch();
    $stats['total_events'] = $event_stats['total'];
    $stats['ongoing_events'] = $event_stats['ongoing'];

    // Get sports count
    $sql = "SELECT COUNT(*) as total FROM sports";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['total_sports'] = $stmt->fetch()['total'];

    // Get departments count
    $sql = "SELECT COUNT(*) as total FROM departments";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['total_departments'] = $stmt->fetch()['total'];

    // Get users count
    $sql = "SELECT COUNT(*) as total FROM users";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['total_users'] = $stmt->fetch()['total'];

    // Get live matches count
    $sql = "SELECT COUNT(*) as total FROM matches WHERE status = 'ongoing'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['live_matches'] = $stmt->fetch()['total'];

} catch (Exception $e) {
    error_log("Error getting dashboard stats: " . $e->getMessage());
}

// Get recent activities from audit logs
$recent_activities = [];
try {
    $sql = "SELECT al.*, u.username, u.full_name 
            FROM audit_logs al 
            LEFT JOIN users u ON al.user_id = u.id 
            ORDER BY al.timestamp DESC 
            LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $recent_activities = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting recent activities: " . $e->getMessage());
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-logo p {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            margin-bottom: 30px;
        }
        
        .page-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1rem;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .action-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .action-card:hover {
            transform: translateY(-3px);
        }
        
        .action-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
        
        .action-card p {
            color: #666;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .recent-activities {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #333;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-info {
            flex: 1;
        }
        
        .activity-action {
            font-weight: 600;
            color: #333;
        }
        
        .activity-user {
            font-size: 0.9rem;
            color: #666;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #999;
        }
        
        .welcome-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .welcome-section h2 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .welcome-section p {
            color: #666;
        }
        
        @media (max-width: 768px) {
            .admin-header-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="admin-header-content">
            <div class="admin-logo">
                <h1>SC_IMS Admin</h1>
                <p>Sports Competition Management</p>
            </div>
            <nav class="admin-nav">
                <span>Welcome, <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username']); ?></span>
                <a href="../index.php">Public Dashboard</a>
                <a href="../logout.php">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Admin Dashboard</h1>
            <p class="page-subtitle">Manage your sports competition and event management system</p>
        </div>

        <div class="welcome-section">
            <h2>Welcome to SC_IMS Admin Panel</h2>
            <p>From here you can manage events, sports, departments, users, and monitor all system activities. Use the quick actions below to get started.</p>
        </div>

        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <div class="stat-number"><?php echo $stats['total_events']; ?></div>
                <div class="stat-label">Total Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏃</div>
                <div class="stat-number"><?php echo $stats['ongoing_events']; ?></div>
                <div class="stat-label">Ongoing Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚽</div>
                <div class="stat-number"><?php echo $stats['total_sports']; ?></div>
                <div class="stat-label">Sports Available</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏢</div>
                <div class="stat-number"><?php echo $stats['total_departments']; ?></div>
                <div class="stat-label">Departments</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                <div class="stat-label">System Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔴</div>
                <div class="stat-number"><?php echo $stats['live_matches']; ?></div>
                <div class="stat-label">Live Matches</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="action-card">
                <h3>Event Management</h3>
                <p>Create, edit, and manage sports events and competitions</p>
                <a href="events.php" class="btn">Manage Events</a>
            </div>
            <div class="action-card">
                <h3>Sports Configuration</h3>
                <p>Add and configure different sports and competition types</p>
                <a href="sports.php" class="btn btn-secondary">Manage Sports</a>
            </div>
            <div class="action-card">
                <h3>Department Management</h3>
                <p>Manage participating departments and teams</p>
                <a href="departments.php" class="btn btn-success">Manage Departments</a>
            </div>
            <div class="action-card">
                <h3>User Administration</h3>
                <p>Manage system users, roles, and permissions</p>
                <a href="users.php" class="btn btn-warning">Manage Users</a>
            </div>
            <div class="action-card">
                <h3>Match Management</h3>
                <p>View and manage ongoing matches and brackets</p>
                <a href="matches.php" class="btn" style="background: #17a2b8;">Manage Matches</a>
            </div>
            <div class="action-card">
                <h3>Reports & Analytics</h3>
                <p>Generate reports and view system analytics</p>
                <a href="reports.php" class="btn" style="background: #6f42c1;">View Reports</a>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="recent-activities">
            <div class="card-header">Recent System Activities</div>
            <div class="card-body">
                <?php if (!empty($recent_activities)): ?>
                    <?php foreach ($recent_activities as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-info">
                            <div class="activity-action"><?php echo htmlspecialchars($activity['action']); ?></div>
                            <div class="activity-user">
                                by <?php echo htmlspecialchars($activity['full_name'] ?? $activity['username'] ?? 'System'); ?>
                            </div>
                        </div>
                        <div class="activity-time"><?php echo timeAgo($activity['timestamp']); ?></div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p style="text-align: center; color: #666; font-style: italic;">No recent activities</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
