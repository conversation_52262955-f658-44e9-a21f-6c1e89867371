<?php
/**
 * AJAX endpoint to get tournament formats based on sport type
 */

// Prevent any output before JSON response
ob_start();

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $sport_type = $_POST['sport_type'] ?? 'traditional';
    
    // Map sport types to database categories
    $type_mapping = [
        'traditional' => ['team', 'individual'],
        'team' => ['team'],
        'individual' => ['individual'],
        'academic' => ['academic'],
        'judged' => ['judged'],
        'performance' => ['performance']
    ];
    
    $sport_types = $type_mapping[$sport_type] ?? ['team', 'individual'];
    
    // Create placeholders for IN clause
    $placeholders = str_repeat('?,', count($sport_types) - 1) . '?';
    
    // Get tournament formats that match the sport type
    $stmt = $conn->prepare("
        SELECT id, name, code, description, sport_types, min_participants, max_participants
        FROM tournament_formats 
        WHERE sport_types REGEXP CONCAT('(^|,)(', REPLACE(?, ',', '|'), ')(,|$)')
        ORDER BY name
    ");
    
    $sport_types_string = implode(',', $sport_types);
    $stmt->execute([$sport_types_string]);
    $formats = $stmt->fetchAll();
    
    // If no formats found, create basic ones
    if (empty($formats)) {
        // Create basic tournament formats if they don't exist
        $basic_formats = [
            [
                'name' => 'Single Elimination',
                'code' => 'single_elimination',
                'description' => 'Traditional knockout tournament where teams/participants are eliminated after one loss.',
                'sport_types' => 'team,individual',
                'min_participants' => 2,
                'max_participants' => null
            ],
            [
                'name' => 'Double Elimination',
                'code' => 'double_elimination',
                'description' => 'Two-bracket system with winner\'s and loser\'s brackets.',
                'sport_types' => 'team,individual',
                'min_participants' => 3,
                'max_participants' => null
            ],
            [
                'name' => 'Round Robin',
                'code' => 'round_robin',
                'description' => 'Every team/participant plays every other team/participant once.',
                'sport_types' => 'team,individual',
                'min_participants' => 3,
                'max_participants' => 16
            ],
            [
                'name' => 'Swiss System',
                'code' => 'swiss_system',
                'description' => 'Pairing system commonly used for academic competitions.',
                'sport_types' => 'academic',
                'min_participants' => 4,
                'max_participants' => null
            ]
        ];
        
        foreach ($basic_formats as $format) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $format['name'],
                    $format['code'],
                    $format['description'],
                    $format['sport_types'],
                    $format['min_participants'],
                    $format['max_participants']
                ]);
            } catch (Exception $e) {
                // Format might already exist, continue
            }
        }
        
        // Re-fetch formats
        $stmt = $conn->prepare("
            SELECT id, name, code, description, sport_types, min_participants, max_participants
            FROM tournament_formats 
            WHERE sport_types REGEXP CONCAT('(^|,)(', REPLACE(?, ',', '|'), ')(,|$)')
            ORDER BY name
        ");
        $stmt->execute([$sport_types_string]);
        $formats = $stmt->fetchAll();
    }
    
    // Format the response
    $formatted_formats = [];
    foreach ($formats as $format) {
        $formatted_formats[] = [
            'id' => $format['id'],
            'name' => $format['name'],
            'code' => $format['code'],
            'description' => $format['description'],
            'min_participants' => $format['min_participants'],
            'max_participants' => $format['max_participants'],
            'requires_seeding' => in_array($format['code'], ['single_elimination', 'double_elimination', 'multi_stage']),
            'advancement_type' => in_array($format['code'], ['round_robin', 'swiss_system']) ? 'points' : 'elimination'
        ];
    }
    
    echo json_encode([
        'success' => true,
        'formats' => $formatted_formats
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error loading tournament formats: ' . $e->getMessage()
    ]);
}
?>
