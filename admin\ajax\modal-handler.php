<?php
/**
 * Modal AJAX Handler for SC_IMS Admin Panel
 * Handles all CRUD operations for modal-based forms
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid security token. Please refresh the page and try again.'
    ]);
    exit;
}

// Get request data
$action = $_POST['action'] ?? '';
$entity = $_POST['entity'] ?? '';

try {
    switch ($entity) {
        case 'event':
            handleEventOperations($conn, $action, $_POST);
            break;
        case 'sport':
            handleSportOperations($conn, $action, $_POST);
            break;
        case 'department':
            handleDepartmentOperations($conn, $action, $_POST);
            break;
        case 'match':
            handleMatchOperations($conn, $action, $_POST);
            break;
        default:
            throw new Exception('Invalid entity type');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function handleEventOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            $stmt = $conn->prepare("
                INSERT INTO events (name, description, start_date, end_date, location, status, max_participants_per_department) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $data['start_date'] ?? null,
                $data['end_date'] ?? null,
                sanitizeInput($data['location'] ?? ''),
                $data['status'] ?? 'upcoming',
                $data['max_participants_per_department'] ?? null
            ]);
            
            $eventId = $conn->lastInsertId();
            logAdminActivity('CREATE_EVENT', 'events', $eventId, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Event created successfully',
                'id' => $eventId
            ]);
            break;
            
        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Event ID is required');
            
            $stmt = $conn->prepare("
                UPDATE events 
                SET name = ?, description = ?, start_date = ?, end_date = ?, location = ?, status = ?, max_participants_per_department = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $data['start_date'] ?? null,
                $data['end_date'] ?? null,
                sanitizeInput($data['location'] ?? ''),
                $data['status'] ?? 'upcoming',
                $data['max_participants_per_department'] ?? null,
                $id
            ]);
            
            logAdminActivity('UPDATE_EVENT', 'events', $id, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Event updated successfully'
            ]);
            break;
            
        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Event ID is required');
            
            // Get event name for logging
            $stmt = $conn->prepare("SELECT name FROM events WHERE id = ?");
            $stmt->execute([$id]);
            $event = $stmt->fetch();
            
            if (!$event) throw new Exception('Event not found');
            
            $stmt = $conn->prepare("DELETE FROM events WHERE id = ?");
            $stmt->execute([$id]);
            
            logAdminActivity('DELETE_EVENT', 'events', $id, $event, null);
            
            echo json_encode([
                'success' => true,
                'message' => 'Event deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Invalid action for events');
    }
}

function handleSportOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            // Get sport type info for legacy type field
            $sport_type_id = $data['sport_type_id'] ?? null;
            $legacy_type = 'traditional'; // default

            if ($sport_type_id) {
                $stmt = $conn->prepare("SELECT category FROM sport_types WHERE id = ?");
                $stmt->execute([$sport_type_id]);
                $sport_type = $stmt->fetch();
                if ($sport_type) {
                    $legacy_type = $sport_type['category'];
                }
            }

            $stmt = $conn->prepare("
                INSERT INTO sports (name, description, type, scoring_method, bracket_format, rules, max_participants, sport_type_id, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $legacy_type,
                $data['scoring_method'] ?? 'point_based',
                $data['bracket_format'] ?? 'single_elimination',
                sanitizeInput($data['rules'] ?? ''),
                $data['max_participants'] ?? 0,
                $sport_type_id,
                true
            ]);

            $sportId = $conn->lastInsertId();
            logAdminActivity('CREATE_SPORT', 'sports', $sportId, null, $data);

            echo json_encode([
                'success' => true,
                'message' => 'Sport created successfully',
                'id' => $sportId
            ]);
            break;
            
        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Sport ID is required');

            // Get sport type info for legacy type field
            $sport_type_id = $data['sport_type_id'] ?? null;
            $legacy_type = 'traditional'; // default

            if ($sport_type_id) {
                $stmt = $conn->prepare("SELECT category FROM sport_types WHERE id = ?");
                $stmt->execute([$sport_type_id]);
                $sport_type = $stmt->fetch();
                if ($sport_type) {
                    $legacy_type = $sport_type['category'];
                }
            }

            $stmt = $conn->prepare("
                UPDATE sports
                SET name = ?, description = ?, type = ?, scoring_method = ?, bracket_format = ?, rules = ?, max_participants = ?, sport_type_id = ?
                WHERE id = ?
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $legacy_type,
                $data['scoring_method'] ?? 'point_based',
                $data['bracket_format'] ?? 'single_elimination',
                sanitizeInput($data['rules'] ?? ''),
                $data['max_participants'] ?? 0,
                $sport_type_id,
                $id
            ]);

            logAdminActivity('UPDATE_SPORT', 'sports', $id, null, $data);

            echo json_encode([
                'success' => true,
                'message' => 'Sport updated successfully'
            ]);
            break;
            
        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Sport ID is required');
            
            // Get sport name for logging
            $stmt = $conn->prepare("SELECT name FROM sports WHERE id = ?");
            $stmt->execute([$id]);
            $sport = $stmt->fetch();
            
            if (!$sport) throw new Exception('Sport not found');
            
            $stmt = $conn->prepare("DELETE FROM sports WHERE id = ?");
            $stmt->execute([$id]);
            
            logAdminActivity('DELETE_SPORT', 'sports', $id, $sport, null);
            
            echo json_encode([
                'success' => true,
                'message' => 'Sport deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Invalid action for sports');
    }
}

function handleDepartmentOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            // Validate color format
            $color_code = sanitizeInput($data['color_code'] ?? '#3498db');
            if (!preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color_code)) {
                throw new Exception('Invalid color format. Please use a valid hex color code.');
            }

            $stmt = $conn->prepare("
                INSERT INTO departments (name, abbreviation, description, color_code, head_of_department, contact_email, contact_phone, location)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                strtoupper(sanitizeInput($data['abbreviation'] ?? '')),
                sanitizeInput($data['description'] ?? ''),
                $color_code,
                sanitizeInput($data['head_of_department'] ?? ''),
                sanitizeInput($data['contact_email'] ?? ''),
                sanitizeInput($data['contact_phone'] ?? ''),
                sanitizeInput($data['location'] ?? '')
            ]);
            
            $deptId = $conn->lastInsertId();
            logAdminActivity('CREATE_DEPARTMENT', 'departments', $deptId, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department created successfully',
                'id' => $deptId
            ]);
            break;
            
        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Department ID is required');

            // Validate color format
            $color_code = sanitizeInput($data['color_code'] ?? '#3498db');
            if (!preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color_code)) {
                throw new Exception('Invalid color format. Please use a valid hex color code.');
            }

            $stmt = $conn->prepare("
                UPDATE departments
                SET name = ?, abbreviation = ?, description = ?, color_code = ?, head_of_department = ?, contact_email = ?, contact_phone = ?, location = ?
                WHERE id = ?
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                strtoupper(sanitizeInput($data['abbreviation'] ?? '')),
                sanitizeInput($data['description'] ?? ''),
                $color_code,
                sanitizeInput($data['head_of_department'] ?? ''),
                sanitizeInput($data['contact_email'] ?? ''),
                sanitizeInput($data['contact_phone'] ?? ''),
                sanitizeInput($data['location'] ?? ''),
                $id
            ]);
            
            logAdminActivity('UPDATE_DEPARTMENT', 'departments', $id, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department updated successfully'
            ]);
            break;
            
        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Department ID is required');
            
            // Get department name for logging
            $stmt = $conn->prepare("SELECT name FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            $dept = $stmt->fetch();
            
            if (!$dept) throw new Exception('Department not found');
            
            $stmt = $conn->prepare("DELETE FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            
            logAdminActivity('DELETE_DEPARTMENT', 'departments', $id, $dept, null);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Invalid action for departments');
    }
}

function handleMatchOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            try {
                $stmt = $conn->prepare("INSERT INTO matches (event_sport_id, team1_id, team2_id, round_number, match_number, scheduled_time, venue, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $data['event_sport_id'],
                    $data['team1_id'],
                    !empty($data['team2_id']) ? $data['team2_id'] : null,
                    $data['round_number'] ?? 1,
                    $data['match_number'] ?? 1,
                    !empty($data['scheduled_time']) ? $data['scheduled_time'] : null,
                    $data['venue'] ?? null,
                    $data['status'] ?? 'scheduled'
                ]);

                $matchId = $conn->lastInsertId();
                logAdminActivity('CREATE_MATCH', 'matches', $matchId, null, $data);

                echo json_encode(['success' => true, 'message' => 'Match scheduled successfully!']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to schedule match: ' . $e->getMessage()]);
            }
            break;

        case 'update':
            try {
                $stmt = $conn->prepare("UPDATE matches SET event_sport_id = ?, team1_id = ?, team2_id = ?, round_number = ?, match_number = ?, scheduled_time = ?, venue = ?, status = ? WHERE id = ?");
                $stmt->execute([
                    $data['event_sport_id'],
                    $data['team1_id'],
                    !empty($data['team2_id']) ? $data['team2_id'] : null,
                    $data['round_number'] ?? 1,
                    $data['match_number'] ?? 1,
                    !empty($data['scheduled_time']) ? $data['scheduled_time'] : null,
                    $data['venue'] ?? null,
                    $data['status'] ?? 'scheduled',
                    $data['id']
                ]);

                logAdminActivity('UPDATE_MATCH', 'matches', $data['id'], null, $data);

                echo json_encode(['success' => true, 'message' => 'Match updated successfully!']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to update match: ' . $e->getMessage()]);
            }
            break;

        case 'delete':
            try {
                // Check if match has scores before deleting
                $stmt = $conn->prepare("SELECT COUNT(*) FROM scores WHERE match_id = ?");
                $stmt->execute([$data['id']]);
                $scoreCount = $stmt->fetchColumn();

                if ($scoreCount > 0) {
                    echo json_encode(['success' => false, 'message' => 'Cannot delete match with existing scores. Please remove scores first.']);
                    break;
                }

                $stmt = $conn->prepare("DELETE FROM matches WHERE id = ?");
                $stmt->execute([$data['id']]);

                logAdminActivity('DELETE_MATCH', 'matches', $data['id'], null, null);

                echo json_encode(['success' => true, 'message' => 'Match deleted successfully!']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to delete match: ' . $e->getMessage()]);
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid match action']);
    }
    exit;
}
?>
