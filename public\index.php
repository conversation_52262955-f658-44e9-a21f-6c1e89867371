<?php
/**
 * Public Dashboard for SC_IMS
 * Read-only public interface for viewing live scores, matches, and rankings
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

// Get live matches
$live_matches = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name, s.type as sport_type,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.color_code as team1_color,
                   d2.name as team2_dept_name, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            WHERE m.status = 'ongoing'
            ORDER BY m.actual_start_time DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $live_matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting live matches: " . $e->getMessage());
}

// Get recent results
$recent_results = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.color_code as team1_color,
                   d2.name as team2_dept_name, d2.color_code as team2_color,
                   w.team_name as winner_name, wd.name as winner_dept_name
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            LEFT JOIN registrations w ON m.winner_id = w.id
            LEFT JOIN departments wd ON w.department_id = wd.id
            WHERE m.status = 'completed'
            ORDER BY m.actual_end_time DESC
            LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $recent_results = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting recent results: " . $e->getMessage());
}

// Get upcoming matches
$upcoming_matches = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.color_code as team1_color,
                   d2.name as team2_dept_name, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            WHERE m.status = 'scheduled'
            ORDER BY m.scheduled_time ASC
            LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $upcoming_matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting upcoming matches: " . $e->getMessage());
}

// Get current rankings
$rankings = [];
try {
    $sql = "SELECT r.*, d.name as department_name, d.color_code, e.name as event_name
            FROM rankings r
            JOIN departments d ON r.department_id = d.id
            JOIN events e ON r.event_id = e.id
            WHERE e.status IN ('ongoing', 'completed')
            ORDER BY e.start_date DESC, r.rank_position ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $rankings = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting rankings: " . $e->getMessage());
}

// Get active events
$active_events = [];
try {
    $sql = "SELECT * FROM events WHERE status IN ('ongoing', 'upcoming') ORDER BY start_date ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $active_events = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting active events: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SC_IMS - Sports Competition Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo h1 {
            font-size: 2rem;
            font-weight: 700;
        }

        .logo p {
            font-size: 1rem;
            opacity: 0.9;
        }

        .header-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .header-nav a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .header-nav a:hover {
            background: rgba(255,255,255,0.2);
        }

        .live-indicator {
            background: #ff4757;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .match-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: transform 0.2s ease;
        }

        .match-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .match-card.live {
            border-color: #ff4757;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .match-teams {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }

        .team {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .team-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .vs {
            font-weight: bold;
            color: #666;
        }

        .match-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-live {
            background: #ff4757;
            color: white;
        }

        .status-completed {
            background: #2ed573;
            color: white;
        }

        .status-scheduled {
            background: #ffa502;
            color: white;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .ranking-item:last-child {
            border-bottom: none;
        }

        .rank-position {
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 15px;
            min-width: 30px;
        }

        .rank-position.first {
            color: #ffd700;
        }

        .rank-position.second {
            color: #c0c0c0;
        }

        .rank-position.third {
            color: #cd7f32;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .header-nav {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>SC_IMS</h1>
                <p>Sports Competition & Event Management</p>
            </div>
            <nav class="header-nav">
                <?php if (!empty($live_matches)): ?>
                    <span class="live-indicator">● LIVE</span>
                <?php endif; ?>
                <a href="../referee/">Referee Panel</a>
                <a href="../admin/">Admin Panel</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="dashboard-grid">
            <div class="main-content">
                <!-- Live Matches Section -->
                <?php if (!empty($live_matches)): ?>
                <div class="card">
                    <div class="card-header">
                        🔴 Live Matches
                    </div>
                    <div class="card-body">
                        <?php foreach ($live_matches as $match): ?>
                            <div class="match-card live">
                                <div class="match-header">
                                    <strong><?php echo htmlspecialchars($match['event_name']); ?> - <?php echo htmlspecialchars($match['sport_name']); ?></strong>
                                    <span class="match-status status-live">LIVE</span>
                                </div>
                                <div class="match-teams">
                                    <div class="team">
                                        <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team1_color']); ?>"></div>
                                        <span><?php echo htmlspecialchars($match['team1_dept_name']); ?></span>
                                    </div>
                                    <span class="vs">VS</span>
                                    <div class="team">
                                        <span><?php echo htmlspecialchars($match['team2_dept_name'] ?? 'TBD'); ?></span>
                                        <?php if ($match['team2_color']): ?>
                                            <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team2_color']); ?>"></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if ($match['venue']): ?>
                                    <div style="text-align: center; color: #666; font-size: 0.9rem;">
                                        📍 <?php echo htmlspecialchars($match['venue']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Results Section -->
                <div class="card">
                    <div class="card-header">
                        📊 Recent Results
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_results)): ?>
                            <p style="text-align: center; color: #666;">No recent results available.</p>
                        <?php else: ?>
                            <?php foreach ($recent_results as $match): ?>
                                <div class="match-card">
                                    <div class="match-header">
                                        <strong><?php echo htmlspecialchars($match['event_name']); ?> - <?php echo htmlspecialchars($match['sport_name']); ?></strong>
                                        <span class="match-status status-completed">COMPLETED</span>
                                    </div>
                                    <div class="match-teams">
                                        <div class="team">
                                            <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team1_color']); ?>"></div>
                                            <span><?php echo htmlspecialchars($match['team1_dept_name']); ?></span>
                                        </div>
                                        <span class="vs">VS</span>
                                        <div class="team">
                                            <span><?php echo htmlspecialchars($match['team2_dept_name'] ?? 'TBD'); ?></span>
                                            <?php if ($match['team2_color']): ?>
                                                <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team2_color']); ?>"></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if ($match['winner_name']): ?>
                                        <div style="text-align: center; color: #2ed573; font-weight: bold;">
                                            🏆 Winner: <?php echo htmlspecialchars($match['winner_dept_name']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <!-- Current Rankings -->
                <div class="card">
                    <div class="card-header">
                        🏆 Current Rankings
                    </div>
                    <div class="card-body">
                        <?php if (empty($rankings)): ?>
                            <p style="text-align: center; color: #666;">No rankings available.</p>
                        <?php else: ?>
                            <?php 
                            $current_event = '';
                            foreach ($rankings as $ranking): 
                                if ($current_event !== $ranking['event_name']):
                                    if ($current_event !== '') echo '</div>';
                                    $current_event = $ranking['event_name'];
                                    echo '<h4 style="margin-bottom: 10px; color: #333;">' . htmlspecialchars($current_event) . '</h4>';
                                    echo '<div style="margin-bottom: 20px;">';
                                endif;
                            ?>
                                <div class="ranking-item">
                                    <span class="rank-position <?php 
                                        echo $ranking['rank_position'] == 1 ? 'first' : 
                                             ($ranking['rank_position'] == 2 ? 'second' : 
                                             ($ranking['rank_position'] == 3 ? 'third' : '')); 
                                    ?>">
                                        <?php echo $ranking['rank_position']; ?>
                                    </span>
                                    <div class="team-color" style="background-color: <?php echo htmlspecialchars($ranking['color_code']); ?>; margin-right: 10px;"></div>
                                    <div style="flex: 1;">
                                        <div style="font-weight: 500;"><?php echo htmlspecialchars($ranking['department_name']); ?></div>
                                        <div style="font-size: 0.8rem; color: #666;">
                                            <?php echo $ranking['total_points']; ?> pts | 
                                            <?php echo $ranking['wins']; ?>W-<?php echo $ranking['losses']; ?>L
                                            <?php if ($ranking['draws'] > 0): ?>-<?php echo $ranking['draws']; ?>D<?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php 
                            endforeach; 
                            if ($current_event !== '') echo '</div>';
                            ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Upcoming Matches -->
                <div class="card">
                    <div class="card-header">
                        📅 Upcoming Matches
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcoming_matches)): ?>
                            <p style="text-align: center; color: #666;">No upcoming matches scheduled.</p>
                        <?php else: ?>
                            <?php foreach (array_slice($upcoming_matches, 0, 5) as $match): ?>
                                <div class="match-card">
                                    <div class="match-header">
                                        <strong style="font-size: 0.9rem;"><?php echo htmlspecialchars($match['sport_name']); ?></strong>
                                        <span class="match-status status-scheduled">SCHEDULED</span>
                                    </div>
                                    <div class="match-teams">
                                        <div class="team">
                                            <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team1_color']); ?>"></div>
                                            <span style="font-size: 0.9rem;"><?php echo htmlspecialchars($match['team1_dept_name']); ?></span>
                                        </div>
                                        <span class="vs">VS</span>
                                        <div class="team">
                                            <span style="font-size: 0.9rem;"><?php echo htmlspecialchars($match['team2_dept_name'] ?? 'TBD'); ?></span>
                                            <?php if ($match['team2_color']): ?>
                                                <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team2_color']); ?>"></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php if ($match['scheduled_time']): ?>
                                        <div style="text-align: center; color: #666; font-size: 0.8rem;">
                                            🕒 <?php echo date('M j, Y g:i A', strtotime($match['scheduled_time'])); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh page every 30 seconds for live updates
        setInterval(function() {
            if (document.querySelector('.live-indicator')) {
                location.reload();
            }
        }, 30000);

        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
