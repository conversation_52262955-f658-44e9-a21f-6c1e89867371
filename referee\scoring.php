<?php
/**
 * Scoring Interface for SC_IMS Referee Panel
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require referee or admin access
requireRefereeOrAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$match_id = $_GET['match_id'] ?? null;
if (!$match_id) {
    header('Location: index.php');
    exit;
}

// Get match details
$match = getMatchDetails($conn, $match_id);
if (!$match) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'start_match':
                $result = startMatch($conn, $match_id);
                if ($result['success']) {
                    $message = $result['message'];
                    $match['status'] = 'live';
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update_score':
                $result = updateScore($conn, $match_id, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'end_match':
                $result = endMatch($conn, $match_id, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                    $match['status'] = 'completed';
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get current scores
$current_scores = getCurrentScores($conn, $match_id);

function getMatchDetails($conn, $match_id) {
    $sql = "SELECT m.*, 
            es.bracket_type,
            s.name as sport_name, s.scoring_system, s.type as sport_type,
            e.name as event_name,
            r1.department_id as team1_dept_id, d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            r2.department_id as team2_dept_id, d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$match_id]);
    return $stmt->fetch();
}

function startMatch($conn, $match_id) {
    try {
        $sql = "UPDATE matches SET status = 'live', started_at = NOW() WHERE id = ? AND status = 'scheduled'";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$match_id]);
        
        if ($stmt->rowCount() > 0) {
            logActivity('START_MATCH', 'matches', $match_id);
            return ['success' => true, 'message' => 'Match started successfully!'];
        } else {
            return ['success' => false, 'message' => 'Match cannot be started.'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to start match: ' . $e->getMessage()];
    }
}

function updateScore($conn, $match_id, $data) {
    try {
        $current_user = getCurrentUser();
        
        // Insert or update score
        $sql = "INSERT INTO scores (match_id, team1_score, team2_score, period, referee_id, notes) 
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                team1_score = VALUES(team1_score), 
                team2_score = VALUES(team2_score),
                notes = VALUES(notes),
                updated_at = NOW()";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $match_id,
            $data['team1_score'] ?? 0,
            $data['team2_score'] ?? 0,
            $data['period'] ?? 1,
            $current_user['id'],
            sanitizeInput($data['notes'] ?? '')
        ]);
        
        // Update match timestamp
        $sql = "UPDATE matches SET updated_at = NOW() WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$match_id]);
        
        logActivity('UPDATE_SCORE', 'scores', $match_id);
        return ['success' => true, 'message' => 'Score updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update score: ' . $e->getMessage()];
    }
}

function endMatch($conn, $match_id, $data) {
    try {
        $conn->beginTransaction();
        
        // Update final score
        $current_user = getCurrentUser();
        $sql = "INSERT INTO scores (match_id, team1_score, team2_score, period, referee_id, notes, is_final) 
                VALUES (?, ?, ?, ?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE 
                team1_score = VALUES(team1_score), 
                team2_score = VALUES(team2_score),
                notes = VALUES(notes),
                is_final = 1,
                updated_at = NOW()";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $match_id,
            $data['final_team1_score'] ?? 0,
            $data['final_team2_score'] ?? 0,
            $data['period'] ?? 1,
            $current_user['id'],
            sanitizeInput($data['final_notes'] ?? '')
        ]);
        
        // Determine winner
        $team1_score = intval($data['final_team1_score'] ?? 0);
        $team2_score = intval($data['final_team2_score'] ?? 0);
        $winner_id = null;
        
        if ($team1_score > $team2_score) {
            $winner_id = $data['team1_id'];
        } elseif ($team2_score > $team1_score) {
            $winner_id = $data['team2_id'];
        }
        
        // Update match status
        $sql = "UPDATE matches SET status = 'completed', winner_id = ?, ended_at = NOW() WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$winner_id, $match_id]);
        
        $conn->commit();
        logActivity('END_MATCH', 'matches', $match_id);
        return ['success' => true, 'message' => 'Match completed successfully!'];
    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => 'Failed to end match: ' . $e->getMessage()];
    }
}

function getCurrentScores($conn, $match_id) {
    $sql = "SELECT * FROM scores WHERE match_id = ? ORDER BY period DESC, updated_at DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$match_id]);
    return $stmt->fetch();
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scoring - <?php echo htmlspecialchars($match['sport_name']); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .match-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .teams-scoreboard {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .team-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .team-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .team-score {
            font-size: 3rem;
            font-weight: bold;
            color: #28a745;
            margin: 10px 0;
        }
        
        .vs-divider {
            font-size: 2rem;
            font-weight: bold;
            color: #666;
            text-align: center;
        }
        
        .scoring-interface {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .score-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .team-controls {
            text-align: center;
        }
        
        .score-input {
            font-size: 2rem;
            text-align: center;
            width: 100px;
            margin: 10px auto;
            display: block;
        }
        
        .quick-score-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 10px;
        }
        
        .quick-score-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #28a745;
            background: white;
            color: #28a745;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .quick-score-btn:hover {
            background: #28a745;
            color: white;
        }
        
        .match-status {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .status-live {
            color: #28a745;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        .timer-display {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #495057;
        }
        
        .period-selector {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .criteria-scoring {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .criteria-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>Match Scoring</h1>
                </div>
                <div class="admin-nav">
                    <span>Welcome, <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username']); ?></span>
                    <a href="index.php">Back to Dashboard</a>
                    <a href="../logout.php">Logout</a>
                </div>
            </header>

            <div class="admin-content">
                <div class="match-header">
                    <h2><?php echo htmlspecialchars($match['sport_name']); ?></h2>
                    <p><strong>Event:</strong> <?php echo htmlspecialchars($match['event_name']); ?> - Round <?php echo $match['round']; ?></p>
                    <p><strong>Scoring System:</strong> <?php echo ucfirst($match['scoring_system']); ?></p>
                    <?php if ($match['scheduled_time']): ?>
                        <p><strong>Scheduled:</strong> <?php echo formatDateTime($match['scheduled_time']); ?></p>
                    <?php endif; ?>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <!-- Match Status -->
                <div class="match-status">
                    <h3>Match Status: 
                        <span class="status-<?php echo $match['status']; ?>">
                            <?php echo ucfirst($match['status']); ?>
                        </span>
                    </h3>
                    <?php if ($match['status'] === 'live'): ?>
                        <div class="timer-display" id="matchTimer">
                            Match in progress...
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Teams Scoreboard -->
                <div class="teams-scoreboard">
                    <div class="team-card" style="border-left: 5px solid <?php echo $match['team1_color']; ?>">
                        <div class="team-name"><?php echo htmlspecialchars($match['team1_name']); ?></div>
                        <div class="team-score" id="team1Score"><?php echo $current_scores['team1_score'] ?? 0; ?></div>
                        <div><?php echo htmlspecialchars($match['team1_abbr']); ?></div>
                    </div>
                    
                    <div class="vs-divider">VS</div>
                    
                    <div class="team-card" style="border-left: 5px solid <?php echo $match['team2_color'] ?? '#ccc'; ?>">
                        <div class="team-name"><?php echo htmlspecialchars($match['team2_name'] ?? 'BYE'); ?></div>
                        <div class="team-score" id="team2Score"><?php echo $current_scores['team2_score'] ?? 0; ?></div>
                        <div><?php echo htmlspecialchars($match['team2_abbr'] ?? ''); ?></div>
                    </div>
                </div>

                <?php if ($match['status'] === 'scheduled'): ?>
                    <!-- Start Match -->
                    <div class="scoring-interface">
                        <h3>Start Match</h3>
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="start_match">
                            <button type="submit" class="btn btn-success btn-lg">Start Match</button>
                        </form>
                    </div>
                <?php elseif ($match['status'] === 'live'): ?>
                    <!-- Live Scoring Interface -->
                    <div class="scoring-interface">
                        <h3>Live Scoring</h3>
                        
                        <?php if ($match['scoring_system'] === 'points'): ?>
                            <!-- Points-based scoring -->
                            <form method="POST" id="scoringForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_score">
                                
                                <div class="period-selector">
                                    <label for="period">Period/Set:</label>
                                    <select name="period" id="period" class="form-control" style="width: auto; display: inline-block;">
                                        <option value="1" <?php echo ($current_scores['period'] ?? 1) == 1 ? 'selected' : ''; ?>>1</option>
                                        <option value="2" <?php echo ($current_scores['period'] ?? 1) == 2 ? 'selected' : ''; ?>>2</option>
                                        <option value="3" <?php echo ($current_scores['period'] ?? 1) == 3 ? 'selected' : ''; ?>>3</option>
                                        <option value="4" <?php echo ($current_scores['period'] ?? 1) == 4 ? 'selected' : ''; ?>>4</option>
                                    </select>
                                </div>

                                <div class="score-controls">
                                    <div class="team-controls">
                                        <h4><?php echo htmlspecialchars($match['team1_name']); ?></h4>
                                        <input type="number" name="team1_score" id="team1ScoreInput" class="form-control score-input" 
                                               value="<?php echo $current_scores['team1_score'] ?? 0; ?>" min="0">
                                        <div class="quick-score-buttons">
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team1', 1)">+1</button>
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team1', 2)">+2</button>
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team1', 3)">+3</button>
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team1', -1)">-1</button>
                                        </div>
                                    </div>
                                    
                                    <?php if ($match['team2_name']): ?>
                                    <div class="team-controls">
                                        <h4><?php echo htmlspecialchars($match['team2_name']); ?></h4>
                                        <input type="number" name="team2_score" id="team2ScoreInput" class="form-control score-input" 
                                               value="<?php echo $current_scores['team2_score'] ?? 0; ?>" min="0">
                                        <div class="quick-score-buttons">
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team2', 1)">+1</button>
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team2', 2)">+2</button>
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team2', 3)">+3</button>
                                            <button type="button" class="quick-score-btn" onclick="adjustScore('team2', -1)">-1</button>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group">
                                    <label for="notes">Notes</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"><?php echo htmlspecialchars($current_scores['notes'] ?? ''); ?></textarea>
                                </div>

                                <div style="text-align: center; margin-top: 20px;">
                                    <button type="submit" class="btn btn-primary">Update Score</button>
                                    <button type="button" class="btn btn-warning" onclick="showEndMatchModal()">End Match</button>
                                </div>
                            </form>
                        <?php elseif ($match['scoring_system'] === 'criteria'): ?>
                            <!-- Criteria-based scoring (for judged events) -->
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_score">
                                
                                <div class="criteria-scoring">
                                    <div class="criteria-item">
                                        <h5>Technical Skill (1-10)</h5>
                                        <input type="number" name="team1_technical" class="form-control" min="1" max="10" value="<?php echo $current_scores['team1_score'] ?? 5; ?>">
                                    </div>
                                    <div class="criteria-item">
                                        <h5>Presentation (1-10)</h5>
                                        <input type="number" name="team1_presentation" class="form-control" min="1" max="10" value="5">
                                    </div>
                                    <div class="criteria-item">
                                        <h5>Creativity (1-10)</h5>
                                        <input type="number" name="team1_creativity" class="form-control" min="1" max="10" value="5">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="notes">Judge Comments</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="4"><?php echo htmlspecialchars($current_scores['notes'] ?? ''); ?></textarea>
                                </div>

                                <div style="text-align: center; margin-top: 20px;">
                                    <button type="submit" class="btn btn-primary">Update Scores</button>
                                    <button type="button" class="btn btn-warning" onclick="showEndMatchModal()">Finalize Judging</button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <!-- Completed Match -->
                    <div class="scoring-interface">
                        <h3>Match Completed</h3>
                        <?php if ($current_scores): ?>
                            <p><strong>Final Score:</strong> <?php echo $current_scores['team1_score']; ?> - <?php echo $current_scores['team2_score']; ?></p>
                            <?php if ($current_scores['notes']): ?>
                                <p><strong>Notes:</strong> <?php echo htmlspecialchars($current_scores['notes']); ?></p>
                            <?php endif; ?>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-primary">Back to Dashboard</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- End Match Modal -->
    <div id="endMatchModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>End Match</h3>
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="end_match">
                <input type="hidden" name="team1_id" value="<?php echo $match['team1_id']; ?>">
                <input type="hidden" name="team2_id" value="<?php echo $match['team2_id']; ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Final Score - <?php echo htmlspecialchars($match['team1_name']); ?></label>
                        <input type="number" name="final_team1_score" class="form-control" value="<?php echo $current_scores['team1_score'] ?? 0; ?>" min="0" required>
                    </div>
                    <?php if ($match['team2_name']): ?>
                    <div class="form-group">
                        <label>Final Score - <?php echo htmlspecialchars($match['team2_name']); ?></label>
                        <input type="number" name="final_team2_score" class="form-control" value="<?php echo $current_scores['team2_score'] ?? 0; ?>" min="0" required>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label>Final Notes</label>
                    <textarea name="final_notes" class="form-control" rows="3"><?php echo htmlspecialchars($current_scores['notes'] ?? ''); ?></textarea>
                </div>
                
                <div style="text-align: center;">
                    <button type="submit" class="btn btn-success">End Match</button>
                    <button type="button" class="btn btn-secondary" onclick="hideEndMatchModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
    <script>
        function adjustScore(team, points) {
            const input = document.getElementById(team + 'ScoreInput');
            const currentScore = parseInt(input.value) || 0;
            const newScore = Math.max(0, currentScore + points);
            input.value = newScore;
            
            // Update display
            document.getElementById(team + 'Score').textContent = newScore;
            
            // Auto-submit form
            document.getElementById('scoringForm').submit();
        }
        
        function showEndMatchModal() {
            document.getElementById('endMatchModal').style.display = 'block';
        }
        
        function hideEndMatchModal() {
            document.getElementById('endMatchModal').style.display = 'none';
        }
        
        // Auto-refresh for live matches
        <?php if ($match['status'] === 'live'): ?>
        setInterval(function() {
            // Only refresh if no form is being filled
            if (!document.activeElement || document.activeElement.tagName !== 'INPUT') {
                location.reload();
            }
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
