<?php
/**
 * User Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'create':
                $result = createUser($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update':
                $result = updateUser($conn, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'delete':
                $result = deleteUser($conn, $_POST['user_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'reset_password':
                $result = resetUserPassword($conn, $_POST['user_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get all users
$users = getAllUsers($conn);

// Get user for editing
$edit_user = null;
if (isset($_GET['edit'])) {
    $edit_user = getUserById($conn, $_GET['edit']);
}

function createUser($conn, $data) {
    try {
        // Check if username already exists
        $sql = "SELECT id FROM users WHERE username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([sanitizeInput($data['username'])]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Username already exists.'];
        }

        // Check if email already exists
        $sql = "SELECT id FROM users WHERE email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([sanitizeInput($data['email'])]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Email already exists.'];
        }

        $sql = "INSERT INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['username']),
            sanitizeInput($data['email']),
            password_hash($data['password'], PASSWORD_DEFAULT),
            sanitizeInput($data['full_name']),
            $data['role']
        ]);
        
        logActivity('CREATE_USER', 'users', $conn->lastInsertId());
        return ['success' => true, 'message' => 'User created successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to create user: ' . $e->getMessage()];
    }
}

function updateUser($conn, $data) {
    try {
        $sql = "UPDATE users SET username = ?, email = ?, full_name = ?, role = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            sanitizeInput($data['username']),
            sanitizeInput($data['email']),
            sanitizeInput($data['full_name']),
            $data['role'],
            $data['user_id']
        ]);
        
        logActivity('UPDATE_USER', 'users', $data['user_id']);
        return ['success' => true, 'message' => 'User updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update user: ' . $e->getMessage()];
    }
}

function deleteUser($conn, $user_id) {
    try {
        // Don't allow deleting the current user
        if ($user_id == $_SESSION['user_id']) {
            return ['success' => false, 'message' => 'Cannot delete your own account.'];
        }

        $sql = "DELETE FROM users WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$user_id]);
        
        logActivity('DELETE_USER', 'users', $user_id);
        return ['success' => true, 'message' => 'User deleted successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to delete user: ' . $e->getMessage()];
    }
}

function resetUserPassword($conn, $user_id) {
    try {
        $new_password = 'password123'; // Default password
        $sql = "UPDATE users SET password = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            password_hash($new_password, PASSWORD_DEFAULT),
            $user_id
        ]);
        
        logActivity('RESET_PASSWORD', 'users', $user_id);
        return ['success' => true, 'message' => 'Password reset to: ' . $new_password];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to reset password: ' . $e->getMessage()];
    }
}

$current_user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .users-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .user-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .users-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .role-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .role-admin { background: #dc3545; color: white; }
        .role-referee { background: #28a745; color: white; }
        .role-viewer { background: #6c757d; color: white; }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-details h4 {
            margin: 0;
            color: #333;
        }
        
        .user-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .last-login {
            font-size: 0.8rem;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="admin-main">
            <header class="admin-header">
                <div>
                    <button class="sidebar-toggle">☰</button>
                    <h1>User Management</h1>
                </div>
                <div class="admin-nav">
                    <span>Welcome, <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username']); ?></span>
                    <a href="index.php">Dashboard</a>
                    <a href="../index.php">Public View</a>
                    <a href="../logout.php">Logout</a>
                </div>
            </header>

            <div class="admin-content">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <div class="users-header">
                    <h2><?php echo $edit_user ? 'Edit User' : 'Add New User'; ?></h2>
                    <?php if ($edit_user): ?>
                        <a href="users.php" class="btn btn-secondary">Cancel Edit</a>
                    <?php endif; ?>
                </div>

                <!-- User Form -->
                <div class="user-form">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $edit_user ? 'update' : 'create'; ?>">
                        <?php if ($edit_user): ?>
                            <input type="hidden" name="user_id" value="<?php echo $edit_user['id']; ?>">
                        <?php endif; ?>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="username">Username *</label>
                                <input type="text" id="username" name="username" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_user['username'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input type="email" id="email" name="email" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_user['email'] ?? ''); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="role">Role</label>
                                <select id="role" name="role" class="form-control">
                                    <option value="viewer" <?php echo ($edit_user['role'] ?? '') === 'viewer' ? 'selected' : ''; ?>>Viewer</option>
                                    <option value="referee" <?php echo ($edit_user['role'] ?? '') === 'referee' ? 'selected' : ''; ?>>Referee</option>
                                    <option value="admin" <?php echo ($edit_user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="full_name">Full Name</label>
                                <input type="text" id="full_name" name="full_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($edit_user['full_name'] ?? ''); ?>">
                            </div>
                            <?php if (!$edit_user): ?>
                            <div class="form-group">
                                <label for="password">Password *</label>
                                <input type="password" id="password" name="password" class="form-control" required>
                            </div>
                            <?php endif; ?>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <?php echo $edit_user ? 'Update User' : 'Create User'; ?>
                        </button>
                    </form>
                </div>

                <!-- Users Table -->
                <div class="users-table">
                    <div class="card-header">
                        <h3>All Users</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($users)): ?>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Role</th>
                                        <th>Last Login</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="user-info">
                                                <div class="user-avatar">
                                                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                                </div>
                                                <div class="user-details">
                                                    <h4><?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?></h4>
                                                    <p>@<?php echo htmlspecialchars($user['username']); ?> • <?php echo htmlspecialchars($user['email']); ?></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="role-badge role-<?php echo $user['role']; ?>">
                                                <?php echo ucfirst($user['role']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="last-login">
                                                <?php echo $user['last_login'] ? timeAgo($user['last_login']) : 'Never'; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="last-login">
                                                <?php echo formatDate($user['created_at']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="users.php?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Reset password for this user?');">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="action" value="reset_password">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-warning">Reset Password</button>
                                                    </form>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?');">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                                    </form>
                                                <?php else: ?>
                                                    <span class="btn btn-sm btn-secondary" style="opacity: 0.5;">Current User</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No users found.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin.js"></script>
</body>
</html>
