<?php
/**
 * AJAX Handler for Event Management Operations
 * Handles sports management, registrations, and other event-specific operations
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'remove_sport':
            $event_sport_id = $_POST['event_sport_id'] ?? 0;
            if (!$event_sport_id) {
                throw new Exception('Event sport ID is required');
            }
            
            // Start transaction
            $conn->beginTransaction();
            
            // Delete matches first (foreign key constraint)
            $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = ?");
            $stmt->execute([$event_sport_id]);
            
            // Delete registrations
            $stmt = $conn->prepare("DELETE FROM registrations WHERE event_sport_id = ?");
            $stmt->execute([$event_sport_id]);
            
            // Delete event sport
            $stmt = $conn->prepare("DELETE FROM event_sports WHERE id = ?");
            $stmt->execute([$event_sport_id]);
            
            $conn->commit();
            
            logAdminActivity('REMOVE_SPORT_FROM_EVENT', 'event_sports', $event_sport_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Sport removed from event successfully'
            ]);
            break;
            
        case 'add_sport':
            $event_id = $_POST['event_id'] ?? 0;
            $sport_id = $_POST['sport_id'] ?? 0;
            $tournament_format_id = $_POST['tournament_format_id'] ?? null;
            $seeding_method = $_POST['seeding_method'] ?? 'random';
            $max_teams = $_POST['max_teams'] ?? null;
            $registration_deadline = $_POST['registration_deadline'] ?? null;

            if (!$event_id || !$sport_id || !$tournament_format_id) {
                throw new Exception('Event ID, Sport ID, and Tournament Format are required');
            }

            // Check if sport already added to event
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$event_id, $sport_id]);
            if ($stmt->fetch()) {
                throw new Exception('Sport already added to this event');
            }

            // Get tournament format details for backward compatibility
            $stmt = $conn->prepare("SELECT code FROM tournament_formats WHERE id = ?");
            $stmt->execute([$tournament_format_id]);
            $format = $stmt->fetch();
            $bracket_type = $format ? $format['code'] : 'single_elimination';

            // Insert event sport
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id, bracket_type, max_teams, registration_deadline)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_id,
                $sport_id,
                $bracket_type,
                $max_teams ?: null,
                $registration_deadline ?: null
            ]);

            $event_sport_id = $conn->lastInsertId();

            // Create tournament structure if format is selected
            if ($tournament_format_id) {
                require_once '../includes/tournament_manager.php';
                $tournamentManager = new TournamentManager($conn);

                try {
                    // Get sport name for tournament name
                    $stmt = $conn->prepare("SELECT name FROM sports WHERE id = ?");
                    $stmt->execute([$sport_id]);
                    $sport = $stmt->fetch();
                    $tournamentName = $sport['name'] . ' Tournament';

                    $config = [
                        'seeding_method' => $seeding_method,
                        'scoring_config' => [
                            'points_win' => 3,
                            'points_draw' => 1,
                            'points_loss' => 0
                        ]
                    ];

                    // Create tournament structure (will be populated when registrations are complete)
                    $tournamentId = $tournamentManager->createTournament($event_sport_id, $tournament_format_id, $tournamentName, $config);

                } catch (Exception $e) {
                    // Log tournament creation error but don't fail the sport addition
                    error_log("Tournament creation failed for event_sport_id {$event_sport_id}: " . $e->getMessage());
                }
            }

            logAdminActivity($conn, $_SESSION['admin_id'], 'ADD_SPORT_TO_EVENT',
                "Added sport to event (Event: {$event_id}, Sport: {$sport_id}, Format: {$tournament_format_id})");

            echo json_encode([
                'success' => true,
                'message' => 'Sport added to event successfully with tournament structure',
                'id' => $event_sport_id,
                'tournament_id' => $tournamentId ?? null
            ]);
            break;
            
        case 'register_department':
            $event_sport_id = $_POST['event_sport_id'] ?? 0;
            $department_id = $_POST['department_id'] ?? 0;
            $team_name = $_POST['team_name'] ?? '';
            $participants = $_POST['participants'] ?? '';
            $status = $_POST['status'] ?? 'pending';
            
            if (!$event_sport_id || !$department_id) {
                throw new Exception('Event sport ID and Department ID are required');
            }
            
            // Check if department already registered
            $stmt = $conn->prepare("SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?");
            $stmt->execute([$event_sport_id, $department_id]);
            if ($stmt->fetch()) {
                throw new Exception('Department already registered for this sport');
            }
            
            // Process participants
            $participant_list = [];
            if (!empty($participants)) {
                $lines = explode("\n", trim($participants));
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        $participant_list[] = $line;
                    }
                }
            }
            
            $stmt = $conn->prepare("
                INSERT INTO registrations (event_sport_id, department_id, team_name, participants, status) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_sport_id,
                $department_id,
                $team_name ?: null,
                json_encode($participant_list),
                $status
            ]);
            
            $registration_id = $conn->lastInsertId();
            logAdminActivity('REGISTER_DEPARTMENT', 'registrations', $registration_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department registered successfully',
                'id' => $registration_id
            ]);
            break;
            
        case 'update_registration_status':
            $registration_id = $_POST['registration_id'] ?? 0;
            $status = $_POST['status'] ?? '';
            
            if (!$registration_id || !$status) {
                throw new Exception('Registration ID and status are required');
            }
            
            $stmt = $conn->prepare("UPDATE registrations SET status = ? WHERE id = ?");
            $stmt->execute([$status, $registration_id]);
            
            logAdminActivity('UPDATE_REGISTRATION_STATUS', 'registrations', $registration_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Registration status updated successfully'
            ]);
            break;
            
        case 'delete_registration':
            $registration_id = $_POST['registration_id'] ?? 0;
            
            if (!$registration_id) {
                throw new Exception('Registration ID is required');
            }
            
            // Start transaction
            $conn->beginTransaction();
            
            // Delete matches involving this registration
            $stmt = $conn->prepare("DELETE FROM matches WHERE team1_id = ? OR team2_id = ?");
            $stmt->execute([$registration_id, $registration_id]);
            
            // Delete registration
            $stmt = $conn->prepare("DELETE FROM registrations WHERE id = ?");
            $stmt->execute([$registration_id]);
            
            $conn->commit();
            
            logAdminActivity('DELETE_REGISTRATION', 'registrations', $registration_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Registration deleted successfully'
            ]);
            break;

        case 'announce_winner':
            $event_id = $_POST['event_id'] ?? 0;

            if (!$event_id) {
                throw new Exception('Event ID is required');
            }

            // Get event details
            $stmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $event = $stmt->fetch();

            if (!$event) {
                throw new Exception('Event not found');
            }

            // Get winner (top department by points)
            $stmt = $conn->prepare("
                SELECT
                    d.id, d.name as department_name, d.abbreviation,
                    COALESCE(SUM(CASE
                        WHEN m.winner_id = r.id THEN 3
                        WHEN m.status = 'completed' AND m.winner_id IS NULL AND (m.team1_id = r.id OR m.team2_id = r.id) THEN 1
                        ELSE 0
                    END), 0) as total_points
                FROM departments d
                LEFT JOIN registrations r ON d.id = r.department_id
                LEFT JOIN event_sports es ON r.event_sport_id = es.id AND es.event_id = ?
                LEFT JOIN matches m ON (m.team1_id = r.id OR m.team2_id = r.id) AND m.status = 'completed'
                WHERE d.id IN (
                    SELECT DISTINCT r2.department_id
                    FROM registrations r2
                    JOIN event_sports es2 ON r2.event_sport_id = es2.id
                    WHERE es2.event_id = ?
                )
                GROUP BY d.id, d.name, d.abbreviation
                ORDER BY total_points DESC
                LIMIT 1
            ");
            $stmt->execute([$event_id, $event_id]);
            $winner = $stmt->fetch();

            if (!$winner) {
                throw new Exception('No winner could be determined');
            }

            // Update event status and winner
            $stmt = $conn->prepare("UPDATE events SET status = 'completed', winner_id = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$winner['id'], $event_id]);

            logAdminActivity('ANNOUNCE_WINNER', 'events', $event_id, "Winner: {$winner['department_name']} ({$winner['total_points']} points)");

            echo json_encode([
                'success' => true,
                'message' => "Winner announced: {$winner['department_name']} with {$winner['total_points']} points!",
                'winner' => $winner
            ]);
            break;

        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
