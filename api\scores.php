<?php
/**
 * Scores API for SC_IMS
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Set JSON header
header('Content-Type: application/json');

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($conn, $action);
            break;
        case 'POST':
            handlePostRequest($conn, $action);
            break;
        case 'PUT':
            handlePutRequest($conn, $action);
            break;
        case 'DELETE':
            handleDeleteRequest($conn, $action);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}

function handleGetRequest($conn, $action) {
    switch ($action) {
        case 'live_scores':
            getLiveScores($conn);
            break;
        case 'match_scores':
            getMatchScoresAPI($conn);
            break;
        case 'rankings':
            getRankingsAPI($conn);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handlePostRequest($conn, $action) {
    // Require referee access for score updates
    if (!isReferee()) {
        http_response_code(403);
        echo json_encode(['error' => 'Unauthorized']);
        return;
    }

    switch ($action) {
        case 'update_score':
            updateScore($conn);
            break;
        case 'complete_match':
            completeMatch($conn);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handlePutRequest($conn, $action) {
    // Handle PUT requests (updates)
    handlePostRequest($conn, $action);
}

function handleDeleteRequest($conn, $action) {
    // Require admin access for deletions
    if (!isAdmin()) {
        http_response_code(403);
        echo json_encode(['error' => 'Unauthorized']);
        return;
    }

    switch ($action) {
        case 'delete_score':
            deleteScore($conn);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function getLiveScores($conn) {
    $live_matches = getLiveMatches($conn);
    $scores_data = [];

    foreach ($live_matches as $match) {
        $scores = getMatchScores($conn, $match['id']);
        $scores_data[] = [
            'match' => $match,
            'scores' => $scores
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $scores_data,
        'timestamp' => time()
    ]);
}

function getMatchScoresAPI($conn) {
    $match_id = $_GET['match_id'] ?? null;
    
    if (!$match_id) {
        http_response_code(400);
        echo json_encode(['error' => 'Match ID required']);
        return;
    }

    $scores = getMatchScores($conn, $match_id);
    
    echo json_encode([
        'success' => true,
        'data' => $scores
    ]);
}

function getRankingsAPI($conn) {
    $event_id = $_GET['event_id'] ?? null;
    
    if (!$event_id) {
        http_response_code(400);
        echo json_encode(['error' => 'Event ID required']);
        return;
    }

    $rankings = getEventRankings($conn, $event_id);
    
    echo json_encode([
        'success' => true,
        'data' => $rankings
    ]);
}

function updateScore($conn) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $match_id = $input['match_id'] ?? null;
    $team_id = $input['team_id'] ?? null;
    $score_value = $input['score_value'] ?? null;
    $score_type = $input['score_type'] ?? 'final';
    $score_details = $input['score_details'] ?? null;

    if (!$match_id || !$team_id || $score_value === null) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }

    try {
        $conn->beginTransaction();

        // Insert or update score
        $sql = "INSERT INTO scores (match_id, team_id, score_value, score_type, score_details, recorded_by) 
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                score_value = VALUES(score_value),
                score_details = VALUES(score_details),
                timestamp = CURRENT_TIMESTAMP,
                recorded_by = VALUES(recorded_by)";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $match_id,
            $team_id,
            $score_value,
            $score_type,
            $score_details ? json_encode($score_details) : null,
            $_SESSION['user_id']
        ]);

        // Log the score update
        logActivity('UPDATE_SCORE', 'scores', $conn->lastInsertId(), null, [
            'match_id' => $match_id,
            'team_id' => $team_id,
            'score_value' => $score_value
        ]);

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Score updated successfully'
        ]);

    } catch (Exception $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update score: ' . $e->getMessage()]);
    }
}

function completeMatch($conn) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $match_id = $input['match_id'] ?? null;
    $winner_id = $input['winner_id'] ?? null;

    if (!$match_id) {
        http_response_code(400);
        echo json_encode(['error' => 'Match ID required']);
        return;
    }

    try {
        $conn->beginTransaction();

        // Update match status
        $sql = "UPDATE matches 
                SET status = 'completed', 
                    actual_end_time = NOW(), 
                    winner_id = ? 
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$winner_id, $match_id]);

        // Get event ID for ranking update
        $sql = "SELECT es.event_id 
                FROM matches m 
                JOIN event_sports es ON m.event_sport_id = es.id 
                WHERE m.id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$match_id]);
        $event = $stmt->fetch();

        if ($event) {
            // Update rankings
            updateEventRankings($conn, $event['event_id']);
        }

        // Log match completion
        logActivity('COMPLETE_MATCH', 'matches', $match_id, null, [
            'winner_id' => $winner_id
        ]);

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Match completed successfully'
        ]);

    } catch (Exception $e) {
        $conn->rollBack();
        http_response_code(500);
        echo json_encode(['error' => 'Failed to complete match: ' . $e->getMessage()]);
    }
}

function deleteScore($conn) {
    $score_id = $_GET['score_id'] ?? null;
    
    if (!$score_id) {
        http_response_code(400);
        echo json_encode(['error' => 'Score ID required']);
        return;
    }

    try {
        $sql = "DELETE FROM scores WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$score_id]);

        // Log score deletion
        logActivity('DELETE_SCORE', 'scores', $score_id);

        echo json_encode([
            'success' => true,
            'message' => 'Score deleted successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete score: ' . $e->getMessage()]);
    }
}
?>
