<?php
/**
 * Tournament Management AJAX Endpoint
 * SC_IMS Sports Competition and Event Management System
 * 
 * Handles AJAX requests for tournament management operations
 */

require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/tournament_manager.php';

// Ensure admin authentication
requireAdmin();

header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_bracket_types':
            handleGetBracketTypes();
            break;
            
        case 'create_tournament':
            handleCreateTournament();
            break;
            
        case 'get_tournament_standings':
            handleGetTournamentStandings();
            break;
            
        case 'get_bracket_visualization':
            handleGetBracketVisualization();
            break;
            
        case 'advance_tournament':
            handleAdvanceTournament();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get available bracket types for a sport
 */
function handleGetBracketTypes() {
    global $conn;
    
    $sportId = $_GET['sport_id'] ?? '';
    if (empty($sportId)) {
        throw new Exception('Sport ID is required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    
    // Get sport type category
    $sportTypeCategory = $tournamentManager->getSportTypeCategory($sportId);
    
    // Get available formats
    $formats = $tournamentManager->getAvailableFormats($sportTypeCategory);
    
    // Format for select options
    $options = [];
    foreach ($formats as $format) {
        $options[] = [
            'value' => $format['id'],
            'text' => $format['name'],
            'description' => $format['description'],
            'min_participants' => $format['min_participants'],
            'max_participants' => $format['max_participants']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'sport_type_category' => $sportTypeCategory,
        'formats' => $options
    ]);
}

/**
 * Create a new tournament
 */
function handleCreateTournament() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $formatId = $_POST['format_id'] ?? '';
    $tournamentName = $_POST['tournament_name'] ?? '';
    $seedingMethod = $_POST['seeding_method'] ?? 'random';
    
    if (empty($eventSportId) || empty($formatId)) {
        throw new Exception('Event sport ID and format ID are required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    
    $config = [
        'seeding_method' => $seedingMethod,
        'scoring_config' => [
            'points_win' => $_POST['points_win'] ?? 3,
            'points_draw' => $_POST['points_draw'] ?? 1,
            'points_loss' => $_POST['points_loss'] ?? 0
        ]
    ];
    
    $tournamentId = $tournamentManager->createTournament($eventSportId, $formatId, $tournamentName, $config);
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'create_tournament', 
        "Created tournament: {$tournamentName} (ID: {$tournamentId})");
    
    echo json_encode([
        'success' => true,
        'message' => 'Tournament created successfully',
        'tournament_id' => $tournamentId
    ]);
}

/**
 * Get tournament standings
 */
function handleGetTournamentStandings() {
    global $conn;
    
    $tournamentId = $_GET['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    $standings = $tournamentManager->getTournamentStandings($tournamentId);
    
    echo json_encode([
        'success' => true,
        'standings' => $standings
    ]);
}

/**
 * Get bracket visualization data
 */
function handleGetBracketVisualization() {
    global $conn;
    
    $tournamentId = $_GET['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    $bracketData = $tournamentManager->getBracketVisualization($tournamentId);
    
    echo json_encode([
        'success' => true,
        'bracket' => $bracketData
    ]);
}

/**
 * Advance tournament to next round
 */
function handleAdvanceTournament() {
    global $conn;
    
    $tournamentId = $_POST['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    $result = $tournamentManager->advanceToNextRound($tournamentId);
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'advance_tournament', 
        "Advanced tournament ID: {$tournamentId} - {$result['message']}");
    
    echo json_encode([
        'success' => true,
        'message' => $result['message'],
        'status' => $result['status']
    ]);
}

/**
 * Get sport details including type category
 */
function getSportDetails($sportId) {
    global $conn;
    
    $sql = "SELECT s.*, st.category as sport_type_category, st.name as sport_type_name
            FROM sports s
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            WHERE s.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$sportId]);
    return $stmt->fetch();
}

/**
 * Validate tournament configuration
 */
function validateTournamentConfig($formatId, $participantCount) {
    global $conn;
    
    $sql = "SELECT min_participants, max_participants FROM tournament_formats WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$formatId]);
    $format = $stmt->fetch();
    
    if (!$format) {
        throw new Exception('Invalid tournament format');
    }
    
    if ($participantCount < $format['min_participants']) {
        throw new Exception("Minimum {$format['min_participants']} participants required for this format");
    }
    
    if ($format['max_participants'] && $participantCount > $format['max_participants']) {
        throw new Exception("Maximum {$format['max_participants']} participants allowed for this format");
    }
    
    return true;
}

/**
 * Get tournament format restrictions for sport type
 */
function getTournamentRestrictions($sportTypeCategory) {
    $restrictions = [
        'individual' => [
            'allowed_formats' => ['elimination_rounds', 'judged_rounds', 'best_performance', 'talent_showcase'],
            'default_seeding' => 'ranking',
            'supports_teams' => false
        ],
        'team' => [
            'allowed_formats' => ['single_elimination', 'double_elimination', 'round_robin', 'multi_stage'],
            'default_seeding' => 'random',
            'supports_teams' => true
        ],
        'academic' => [
            'allowed_formats' => ['swiss_system', 'knockout_rounds', 'round_robin'],
            'default_seeding' => 'ranking',
            'supports_teams' => false
        ],
        'judged' => [
            'allowed_formats' => ['judged_rounds', 'talent_showcase', 'elimination_rounds'],
            'default_seeding' => 'manual',
            'supports_teams' => false
        ]
    ];
    
    return $restrictions[$sportTypeCategory] ?? $restrictions['individual'];
}

/**
 * Get participant count for event sport
 */
function getParticipantCount($eventSportId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) as count FROM registrations 
            WHERE event_sport_id = ? AND status = 'confirmed'";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$eventSportId]);
    $result = $stmt->fetch();
    
    return $result['count'] ?? 0;
}

/**
 * Check if tournament already exists for event sport
 */
function tournamentExists($eventSportId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) as count FROM tournament_structures WHERE event_sport_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$eventSportId]);
    $result = $stmt->fetch();
    
    return $result['count'] > 0;
}
?>
