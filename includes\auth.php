<?php
/**
 * Authentication Functions for SC_IMS
 * Sports Competition and Event Management System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

/**
 * Authenticate user login
 */
function authenticateUser($username, $password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT id, username, email, password_hash, role, full_name, department_id, is_active 
                FROM users 
                WHERE (username = ? OR email = ?) AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && verifyPassword($password, $user['password_hash'])) {
            // Update last login
            $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$user['id']]);
            
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['department_id'] = $user['department_id'];
            $_SESSION['login_time'] = time();
            
            // Log successful login
            logActivity('LOGIN', 'users', $user['id']);
            
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Authentication error: " . $e->getMessage());
        return false;
    }
}

/**
 * Register new user
 */
function registerUser($username, $email, $password, $full_name, $role = 'viewer', $department_id = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Check if username or email already exists
        $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Username or email already exists'];
        }
        
        // Validate password strength
        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            return ['success' => false, 'message' => 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long'];
        }
        
        // Insert new user
        $sql = "INSERT INTO users (username, email, password_hash, role, full_name, department_id) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $username,
            $email,
            hashPassword($password),
            $role,
            $full_name,
            $department_id
        ]);
        
        $user_id = $conn->lastInsertId();
        
        // Log user registration
        logActivity('REGISTER', 'users', $user_id);
        
        return ['success' => true, 'message' => 'User registered successfully', 'user_id' => $user_id];
    } catch (Exception $e) {
        error_log("Registration error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Registration failed. Please try again.'];
    }
}

/**
 * Logout user
 */
function logoutUser() {
    if (isLoggedIn()) {
        logActivity('LOGOUT', 'users', $_SESSION['user_id']);
    }
    
    // Destroy session
    session_destroy();
    
    // Clear session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Redirect to login
    header('Location: ' . APP_URL . '/login.php');
    exit();
}

/**
 * Check session timeout
 */
function checkSessionTimeout() {
    if (isLoggedIn() && isset($_SESSION['login_time'])) {
        if (time() - $_SESSION['login_time'] > SESSION_TIMEOUT) {
            logoutUser();
        }
    }
}

/**
 * Get current user info
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "SELECT u.*, d.name as department_name, d.abbreviation as department_abbr 
                FROM users u 
                LEFT JOIN departments d ON u.department_id = d.id 
                WHERE u.id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting current user: " . $e->getMessage());
        return null;
    }
}

/**
 * Update user profile
 */
function updateUserProfile($user_id, $data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Get current user data for audit log
        $sql = "SELECT * FROM users WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$user_id]);
        $old_data = $stmt->fetch();
        
        $update_fields = [];
        $params = [];
        
        if (isset($data['full_name'])) {
            $update_fields[] = "full_name = ?";
            $params[] = $data['full_name'];
        }
        
        if (isset($data['email'])) {
            // Check if email is already taken by another user
            $sql = "SELECT id FROM users WHERE email = ? AND id != ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$data['email'], $user_id]);
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'Email already exists'];
            }
            
            $update_fields[] = "email = ?";
            $params[] = $data['email'];
        }
        
        if (isset($data['department_id'])) {
            $update_fields[] = "department_id = ?";
            $params[] = $data['department_id'];
        }
        
        if (empty($update_fields)) {
            return ['success' => false, 'message' => 'No data to update'];
        }
        
        $params[] = $user_id;
        
        $sql = "UPDATE users SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        // Log profile update
        logActivity('UPDATE_PROFILE', 'users', $user_id, $old_data, $data);
        
        return ['success' => true, 'message' => 'Profile updated successfully'];
    } catch (Exception $e) {
        error_log("Profile update error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Profile update failed'];
    }
}

/**
 * Change user password
 */
function changeUserPassword($user_id, $current_password, $new_password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Verify current password
        $sql = "SELECT password_hash FROM users WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user || !verifyPassword($current_password, $user['password_hash'])) {
            return ['success' => false, 'message' => 'Current password is incorrect'];
        }
        
        // Validate new password
        if (strlen($new_password) < PASSWORD_MIN_LENGTH) {
            return ['success' => false, 'message' => 'New password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long'];
        }
        
        // Update password
        $sql = "UPDATE users SET password_hash = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([hashPassword($new_password), $user_id]);
        
        // Log password change
        logActivity('CHANGE_PASSWORD', 'users', $user_id);
        
        return ['success' => true, 'message' => 'Password changed successfully'];
    } catch (Exception $e) {
        error_log("Password change error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Password change failed'];
    }
}

/**
 * Reset user password (admin function)
 */
function resetUserPassword($user_id, $new_password) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Validate new password
        if (strlen($new_password) < PASSWORD_MIN_LENGTH) {
            return ['success' => false, 'message' => 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long'];
        }
        
        // Update password
        $sql = "UPDATE users SET password_hash = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([hashPassword($new_password), $user_id]);
        
        // Log password reset
        logActivity('RESET_PASSWORD', 'users', $user_id);
        
        return ['success' => true, 'message' => 'Password reset successfully'];
    } catch (Exception $e) {
        error_log("Password reset error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Password reset failed'];
    }
}

/**
 * Get all users (admin function)
 */
function getAllUsers($conn) {
    $sql = "SELECT u.*, d.name as department_name, d.abbreviation as department_abbr 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            ORDER BY u.created_at DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Update user status (admin function)
 */
function updateUserStatus($user_id, $is_active) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "UPDATE users SET is_active = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$is_active, $user_id]);
        
        // Log status change
        logActivity($is_active ? 'ACTIVATE_USER' : 'DEACTIVATE_USER', 'users', $user_id);
        
        return ['success' => true, 'message' => 'User status updated successfully'];
    } catch (Exception $e) {
        error_log("User status update error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Status update failed'];
    }
}

/**
 * Update user role (admin function)
 */
function updateUserRole($user_id, $role) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $valid_roles = ['admin', 'referee', 'viewer'];
        if (!in_array($role, $valid_roles)) {
            return ['success' => false, 'message' => 'Invalid role'];
        }
        
        $sql = "UPDATE users SET role = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$role, $user_id]);
        
        // Log role change
        logActivity('UPDATE_USER_ROLE', 'users', $user_id, null, ['role' => $role]);
        
        return ['success' => true, 'message' => 'User role updated successfully'];
    } catch (Exception $e) {
        error_log("User role update error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Role update failed'];
    }
}

// Check session timeout on every page load
checkSessionTimeout();
?>
