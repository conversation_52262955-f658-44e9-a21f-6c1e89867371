<?php
/**
 * Match Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'update_status':
                $match_id = $_POST['match_id'] ?? 0;
                $status = $_POST['status'] ?? '';
                
                try {
                    $sql = "UPDATE matches SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    if ($stmt->execute([$status, $match_id])) {
                        $message = "Match status updated successfully.";
                        
                        // Log the action
                        logAdminActivity($current_admin['username'], 'match_status_update',
                                       "Updated match ID $match_id status to $status");
                    } else {
                        $error = "Failed to update match status.";
                    }
                } catch (Exception $e) {
                    $error = "Error updating match: " . $e->getMessage();
                }
                break;
                
            case 'reschedule':
                $match_id = $_POST['match_id'] ?? 0;
                $new_time = $_POST['scheduled_time'] ?? '';
                
                try {
                    $sql = "UPDATE matches SET scheduled_time = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    if ($stmt->execute([$new_time, $match_id])) {
                        $message = "Match rescheduled successfully.";
                        
                        // Log the action
                        logAdminActivity($current_admin['username'], 'match_reschedule',
                                       "Rescheduled match ID $match_id to $new_time");
                    } else {
                        $error = "Failed to reschedule match.";
                    }
                } catch (Exception $e) {
                    $error = "Error rescheduling match: " . $e->getMessage();
                }
                break;
        }
    }
}

// Get all matches with details
$matches = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name, s.type as sport_type,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.abbreviation as team1_abbr,
                   d2.name as team2_dept_name, d2.abbreviation as team2_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            ORDER BY 
                CASE m.status 
                    WHEN 'live' THEN 1 
                    WHEN 'scheduled' THEN 2 
                    WHEN 'completed' THEN 3 
                    ELSE 4 
                END,
                m.scheduled_time ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    $error = "Error loading matches: " . $e->getMessage();
}

// Get match statistics
$stats = [
    'total' => count($matches),
    'scheduled' => count(array_filter($matches, fn($m) => $m['status'] === 'scheduled')),
    'live' => count(array_filter($matches, fn($m) => $m['status'] === 'live')),
    'completed' => count(array_filter($matches, fn($m) => $m['status'] === 'completed'))
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Management - <?php echo APP_NAME; ?></title>

    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .match-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        
        .match-card.live {
            border-left-color: #dc3545;
            background: linear-gradient(90deg, #fff 0%, #fff5f5 100%);
        }
        
        .match-card.completed {
            border-left-color: #28a745;
            opacity: 0.8;
        }
        
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .match-teams {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .team {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .vs {
            color: #666;
            font-weight: normal;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-scheduled {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .status-live {
            background: #ffebee;
            color: #d32f2f;
            animation: pulse 2s infinite;
        }
        
        .status-completed {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .match-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-weight: 600;
        }
        
        .match-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .quick-actions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-trophy"></i>
                        <span>Match Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Match Management</h1>
                <p class="page-description">Monitor and manage all matches across events</p>
            </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert success"><?php echo htmlspecialchars($message); ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert error"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">Total Matches</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['scheduled']; ?></div>
                        <div class="stat-label">Scheduled</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['live']; ?></div>
                        <div class="stat-label">Live</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['completed']; ?></div>
                        <div class="stat-label">Completed</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>Quick Actions</h3>
                    <div class="filters">
                        <button class="filter-btn active" onclick="filterMatches('all')">All Matches</button>
                        <button class="filter-btn" onclick="filterMatches('live')">Live</button>
                        <button class="filter-btn" onclick="filterMatches('scheduled')">Scheduled</button>
                        <button class="filter-btn" onclick="filterMatches('completed')">Completed</button>
                    </div>
                </div>

                <!-- Matches List -->
                <div class="matches-container">
                    <?php if (!empty($matches)): ?>
                        <?php foreach ($matches as $match): ?>
                            <div class="match-card <?php echo $match['status']; ?>" data-status="<?php echo $match['status']; ?>">
                                <div class="match-header">
                                    <div class="match-teams">
                                        <div class="team">
                                            <span><?php echo htmlspecialchars($match['team1_abbr']); ?></span>
                                        </div>
                                        <span class="vs">VS</span>
                                        <div class="team">
                                            <span><?php echo htmlspecialchars($match['team2_abbr'] ?? 'TBD'); ?></span>
                                        </div>
                                    </div>
                                    <span class="status-badge status-<?php echo $match['status']; ?>">
                                        <?php echo ucfirst($match['status']); ?>
                                    </span>
                                </div>
                                
                                <div class="match-info">
                                    <div class="info-item">
                                        <div class="info-label">Event</div>
                                        <div class="info-value"><?php echo htmlspecialchars($match['event_name']); ?></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">Sport</div>
                                        <div class="info-value"><?php echo htmlspecialchars($match['sport_name']); ?></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">Round</div>
                                        <div class="info-value">Round <?php echo $match['round']; ?></div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">Scheduled Time</div>
                                        <div class="info-value"><?php echo formatDateTime($match['scheduled_time']); ?></div>
                                    </div>
                                </div>
                                
                                <div class="match-actions">
                                    <?php if ($match['status'] === 'scheduled'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="update_status">
                                            <input type="hidden" name="match_id" value="<?php echo $match['id']; ?>">
                                            <input type="hidden" name="status" value="live">
                                            <button type="submit" class="btn btn-success btn-sm">Start Match</button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <?php if ($match['status'] === 'live'): ?>
                                        <a href="../referee/scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-primary btn-sm">Score Match</a>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="update_status">
                                            <input type="hidden" name="match_id" value="<?php echo $match['id']; ?>">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="btn btn-warning btn-sm">End Match</button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <?php if ($match['status'] === 'scheduled'): ?>
                                        <button class="btn btn-secondary btn-sm" onclick="showRescheduleForm(<?php echo $match['id']; ?>)">Reschedule</button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-data">
                            <p>No matches found.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function filterMatches(status) {
            const cards = document.querySelectorAll('.match-card');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Filter cards
            cards.forEach(card => {
                if (status === 'all' || card.dataset.status === status) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        function showRescheduleForm(matchId) {
            const newTime = prompt('Enter new date and time (YYYY-MM-DD HH:MM:SS):');
            if (newTime) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="reschedule">
                    <input type="hidden" name="match_id" value="${matchId}">
                    <input type="hidden" name="scheduled_time" value="${newTime}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>

    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
